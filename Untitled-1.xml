<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.caocao</groupId>
        <artifactId>caocao-bom</artifactId>
        <version>1.2.3</version>
    </parent>

    <groupId>com.caocao.barrier.product</groupId>
    <artifactId>barrier-product</artifactId>
    <!-- mvn versions:set -DnewVersion='1.1.27' -->
    <version>1.1.34</version>

    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <modules>
        <module>product-deploy</module>
        <module>product-api</module>
        <module>product-api-impl</module>
        <module>product-dal</module>
        <module>product-service</module>
        <module>product-common</module>
        <module>product-biz</module>
    </modules>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>

        <!--apache common-->
        <commons-fileupload>1.3.3</commons-fileupload>
        <commons-lang.version>2.6</commons-lang.version>
        <commons-collections.version>3.2.1</commons-collections.version>
        <commons-codec.version>1.10</commons-codec.version>
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <httpclient.version>4.5.1</httpclient.version>
        <commons-collections4.version>4.1</commons-collections4.version>

        <!--spring -->
        <spring.version>4.3.8.RELEASE</spring.version>
        <spring-data-redis.version>1.7.1.RELEASE</spring-data-redis.version>
        <kafka.version>2.8.2</kafka.version>
        <spring.kafka.version>2.1.0.RELEASE</spring.kafka.version>
        <mybatis-spring.version>1.2.2</mybatis-spring.version>
        <mybatis.version>3.2.8</mybatis.version>
        <mysql.version>5.1.48</mysql.version>
        <caocao.codis.version>1.0.11</caocao.codis.version>
        <jedis.version>2.9.0</jedis.version>
        <javax.servlet.version>3.1.0</javax.servlet.version>
        <mongodb.version>3.12.14</mongodb.version>

        <!--tool-->
        <servlet.version>2.5</servlet.version>
        <druid.version>1.0.26</druid.version>
        <fastjson.version>1.2.83</fastjson.version>
        <lombok.version>1.16.10</lombok.version>
        <pagehelper.version>4.2.1</pagehelper.version>
        <csnowflake.version>1.0.0.0</csnowflake.version>
        <dozer.version>5.4.0</dozer.version>
        <disruptor.version>3.3.7</disruptor.version>
        <elastic.job.version>2.1.5.1</elastic.job.version>
        <zkclient.version>0.1</zkclient.version>
        <freemarker.version>2.3.27-incubating</freemarker.version>
        <maii-api.version>1.5.5</maii-api.version>
        <mail.version>1.4.7</mail.version>
        <sonar-maven-plugin.version>3.2</sonar-maven-plugin.version>
        <jdom.version>1.0</jdom.version>
        <unitils.version>3.4.2</unitils.version>
        <dbunit.version>2.5.2</dbunit.version>
        <h2database.version>1.4.196</h2database.version>
        <hamcrest-all.version>1.3</hamcrest-all.version>
        <jmockit.version>1.17</jmockit.version>
        <mockito.version>2.23.0</mockito.version>
        <powermock.version>2.0.2</powermock.version>
        <junit.version>4.12</junit.version>
        <javassist.version>3.18.1-GA</javassist.version>
        <joda-time.version>2.10</joda-time.version>
        <spring.integration.redis>4.3.14.RELEASE</spring.integration.redis>
        <!--log-->
        <slf4j.version>1.7.21</slf4j.version>
        <logback-ext-spring.version>0.1.4</logback-ext-spring.version>
        <logback.version>1.2.9</logback.version>

        <maven-surefire-plugin.version>2.19.1</maven-surefire-plugin.version>

        <!--caocao-->
        <guava.version>18.0</guava.version>
        <business.util.version>1.1.4</business.util.version>
        <caocao.util.version>0.7</caocao.util.version>
        <config.version>1.5.5.0</config.version>
        <dictionary.version>1.3.4.1</dictionary.version>
        <dubbo.version>2.7.15.2.CAOCAO</dubbo.version>
        <pay-commons.version>1.0.23.GA</pay-commons.version>
        <pay-dubbo.version>1.0.7</pay-dubbo.version>
        <kafka-client.version>1.0.6</kafka-client.version>
        <pay-redis.version>1.0.32</pay-redis.version>
        <pay-ibatis.version>1.0.3</pay-ibatis.version>
        <pay-bdb.version>1.0.3</pay-bdb.version>
        <notify-api.version>2.2.2</notify-api.version>
        <bss-rpc-model.version>1.0.4</bss-rpc-model.version>
        <cdiamond.version>1.4.2</cdiamond.version>
        <fence-sdk.version>1.6.43</fence-sdk.version>
        <fence-api.version>1.6.43</fence-api.version>
        <car-api.version>1.8.23.0</car-api.version>
        <alibaba.spring.version>1.0.11</alibaba.spring.version>
        <gson.version>2.6.1</gson.version>
        <caocao.mq.version>1.0.0-BETA1</caocao.mq.version>
        <sonar.coverage.exclusions>
            **/CdiamondContanst.java,
            **/CarNearbyDriverListener.java,
            **/CarUpdateLocationListener.java,
            **/DriverMidPointListener.java,
            **/DelayTaskConfig.java,
            **/GraySupport.java,
            **/CaocaoMqConfig.java<!--本次暂时去掉-->

        </sonar.coverage.exclusions>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- fence dependencies begin -->
            <dependency>
               
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-mongodb</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>mongodb-driver-core</artifactId>
                        <groupId>org.mongodb</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mongodb-driver-sync</artifactId>
                        <groupId>org.mongodb</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mongodb-driver</artifactId>
                        <groupId>org.mongodb</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-legacy</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>bson</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-core</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>mongodb-driver-sync</artifactId>
                <version>${mongodb.version}</version>
            </dependency>
            
    </dependencyManagement>
    
</project><?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>barrier-product</artifactId>
        <groupId>com.caocao.barrier.product</groupId>
        <version>1.1.34</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>product-common</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.caocao.bss.component</groupId>
            <artifactId>bss-rpc-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caocao.util</groupId>
            <artifactId>caocao-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.caocao.middleware</groupId>
            <artifactId>codis-proxy</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.pay.framework</groupId>
            <artifactId>pay-commons</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.pay.framework</groupId>
            <artifactId>pay-bdb</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.pay.framework</groupId>
            <artifactId>pay-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.pay.framework</groupId>
            <artifactId>pay-ibatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.pay.framework</groupId>
            <artifactId>pay-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.pay.framework</groupId>
            <artifactId>kafka-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.caocao.cdiamond</groupId>
            <artifactId>cdiamond-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.sgroschupf</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>

        <dependency>
            <groupId>org.dbunit</groupId>
            <artifactId>dbunit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-legacy</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>bson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.bss.fence</groupId>
            <artifactId>fence-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.bss.fence</groupId>
            <artifactId>fence-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.caocao.zhuanche</groupId>
            <artifactId>car-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-core</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- 跳过deploy -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>