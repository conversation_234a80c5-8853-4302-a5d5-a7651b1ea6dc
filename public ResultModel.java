public ResultModel<DingTalkLoginResponseDTO> dingTalkLogin(@RequestBody DingTalkLoginRequestDTO requestDTO) {
        //如果免登码为空，直接返回
        String code = requestDTO.getAuthCode();
        if (StringUtils.isEmpty(code)) {
            return new ResultModel<>(ErrorCodes.CODE_IS_NULL, null);
        }

        DingTalkSecretConfig.DingTalkConfigSceneEnum sceneEnum = getSceneType(requestDTO.getSceneType());
        //通过免登码查询用户信息
        UserInfo userInfo = dingTalkApiService.getUserInfo(sceneEnum, code);
        if (userInfo == null) {
            log.info("钉钉api返回用户为空, authCode:{}", requestDTO.getAuthCode());
            return new ResultModel<>(ErrorCodes.CODE_GET_USER_ERROR, null);
        }

        // 调用钉钉接口查询用户部门信息,产品说曹操中心部门数据有很多比较乱,所以要调钉钉接口拿到部门数据 eg: 出行事业部 > 苏沪区 > 上海
        CompletableFuture<String> deptStringFuture = CompletableFuture.supplyAsync(() -> getDeptStr(userInfo.getUserId(), sceneEnum), ThreadPoolUtil.getDealRecordPool());

        AcUserBO user = queryUser(userInfo.getPhone(), userInfo.getJobNumber());
        if (Objects.isNull(user)) {
            log.info("根据工号手机号查询用户为空, phone:{}, employeeId:{}", userInfo.getPhone(), userInfo.getJobNumber());
            return new ResultModel<>(ErrorCodes.USER_NOT_EXIST, null);
        }
        
        // 检查账号是否被冻结
        if (UserActiveStatusFlagEnum.ACTIVED.getCode() != user.getActiveStatusFlag()) {
            log.info("账号已被冻结, userId:{}, dingTalkUserId:{}", user.getId(), userInfo.getUserId());
            
            // 创建包含DingTalkUserId的响应
            DingTalkLoginResponseDTO response = new DingTalkLoginResponseDTO();
            response.setDingTalkUserId(userInfo.getUserId());
            response.setUserId(user.getId());
            
            // 创建错误结果，并设置data字段
            ResultModel<DingTalkLoginResponseDTO> resultModel = new ResultModel<>();
            resultModel.setError(ErrorCodes.LOGIN_ACCOUNT_NOT_ACTIVE, "账号已被冻结，可能是长时未登录（三个月）或其他原因导致，请联系管理员处理");
            resultModel.setData(response);
            return resultModel;
        }
        
        // 检查权限有效期
        if (!user.getActiveEndTime().after(new Date())) {
            log.info("账号已超过有效期, userId:{}, dingTalkUserId:{}", user.getId(), userInfo.getUserId());
            
            // 创建包含DingTalkUserId的响应
            DingTalkLoginResponseDTO response = new DingTalkLoginResponseDTO();
            response.setDingTalkUserId(userInfo.getUserId());
            response.setUserId(user.getId());
            
            // 创建错误结果，并设置data字段
            ResultModel<DingTalkLoginResponseDTO> resultModel = new ResultModel<>();
            resultModel.setError(ErrorCodes.EXCEEDING_THE_VALIDITY_PERIOD);
            resultModel.setData(response);
            return resultModel;
        }
        
        //校验用户状态
        checkUser(user);
        // 生成登录态凭证
        String ticket = doLogin(user, user.getPhone());

        DingTalkLoginResponseDTO response = buildDingTalkLoginResponseDTO(userInfo, deptStringFuture, user, ticket);
        log.info("[钉钉免登成功] authCode:{}, sceneType:{}, userId:{}, userName:{},ticket:{}, sceneTypeEnum:{}",
                requestDTO.getAuthCode(), requestDTO.getSceneType(), user.getId(), user.getUserName(), ticket, sceneEnum.name());
        return new ResultModel<>(response);
    } 