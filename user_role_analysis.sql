-- TAB1：关联的角色
-- userId、姓名、逻辑系统名、角色ID、角色名称
SELECT 
    usr.id AS userId,
    usr.user_name AS 姓名,
    CASE 
        WHEN usr.type = 0 THEN '曹操'
        WHEN usr.type = 1 THEN '观海'
        ELSE '未知'
    END AS 逻辑系统名,
    role.id AS 角色ID,
    role.role_name AS 角色名称
FROM ac_user usr
INNER JOIN ac_user_role usr_role ON usr.id = usr_role.user_id
INNER JOIN ac_role role ON usr_role.role_id = role.id
WHERE usr.employee_id IN (
    '210', '2821', '10517', '19996', '24666', '31178', 
    '32129', '32483', '32484', '32823', '32878', '32882'
)
AND usr.active_status_flag = 1
AND role.del_flag = 0
ORDER BY usr.employee_id, role.role_name;

-- TAB2：关联的岗位
-- userId、姓名、岗位名称
SELECT 
    usr.id AS userId,
    usr.user_name AS 姓名,
    position.position_name AS 岗位名称
FROM ac_user usr
INNER JOIN ac_user_position usr_position ON usr.id = usr_position.user_id
INNER JOIN ac_position position ON usr_position.position_id = position.id
WHERE usr.employee_id IN (
    '210', '2821', '10517', '19996', '24666', '31178', 
    '32129', '32483', '32484', '32823', '32878', '32882'
)
AND usr.active_status_flag = 1
AND position.del_flag = 0
ORDER BY usr.employee_id, position.position_name;

-- TAB3：直接关联的资源组
-- userId、姓名、逻辑系统名、资源组ID、资源组名称
SELECT 
    usr.id AS userId,
    usr.user_name AS 姓名,
    CASE 
        WHEN usr.type = 0 THEN '曹操'
        WHEN usr.type = 1 THEN '观海'
        ELSE '未知'
    END AS 逻辑系统名,
    og.id AS 资源组ID,
    og.operate_group_name AS 资源组名称
FROM ac_user usr
INNER JOIN ac_user_operate_group user_og ON usr.id = user_og.user_id
INNER JOIN ac_operate_group og ON user_og.operate_group_id = og.id
WHERE usr.employee_id IN (
    '210', '2821', '10517', '19996', '24666', '31178', 
    '32129', '32483', '32484', '32823', '32878', '32882'
)
AND usr.active_status_flag = 1
ORDER BY usr.employee_id, og.operate_group_name; 