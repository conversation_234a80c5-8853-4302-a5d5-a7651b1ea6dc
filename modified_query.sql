-- 【推荐】最优化版本：避免UNION，使用EXISTS提升性能
SELECT DISTINCT
    usr.user_name as '姓名',
    usr.employee_id as '工号',
    GROUP_CONCAT(DISTINCT org.org_name) as '部门'
FROM ac_user usr
LEFT JOIN ac_user_org usr_org ON usr_org.user_id = usr.id
LEFT JOIN ac_org org ON usr_org.org_id = org.id
WHERE usr.active_status_flag = 1
AND (
    -- 通过角色获得权限
    EXISTS (
        SELECT 1 FROM ac_user_role usr_role
        INNER JOIN ac_role role ON usr_role.role_id = role.id AND role.del_flag = 0
        INNER JOIN ac_role_operate_group role_og ON role_og.role_id = role.id
        INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = role_og.operate_group_id
        INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
        WHERE usr_role.user_id = usr.id 
        AND url_res.url IN ('/driverEntry/driverBackPass', '/highSeas/forceBackUpPass')
    )
    OR
    -- 通过岗位获得权限
    EXISTS (
        SELECT 1 FROM ac_user_position usr_position
        INNER JOIN ac_position position ON usr_position.position_id = position.id AND position.del_flag = 0
        INNER JOIN ac_position_operate_group position_og ON position_og.position_id = position.id
        INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = position_og.operate_group_id
        INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
        WHERE usr_position.user_id = usr.id
        AND url_res.url IN ('/driverEntry/driverBackPass', '/highSeas/forceBackUpPass')
    )
    OR
    -- 直接关联权限
    EXISTS (
        SELECT 1 FROM ac_user_operate_group user_og
        INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = user_og.operate_group_id
        INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
        WHERE user_og.user_id = usr.id
        AND url_res.url IN ('/driverEntry/driverBackPass', '/highSeas/forceBackUpPass')
    )
)
GROUP BY usr.user_name, usr.employee_id
ORDER BY usr.user_name ASC, usr.employee_id ASC
LIMIT 200 OFFSET 0;


-- 拆分权限来源（去除城市，使用URL查询）
select 
merge_tab.姓名,
merge_tab.工号,
GROUP_CONCAT(DISTINCT merge_tab.部门) as 部门,
GROUP_CONCAT(DISTINCT merge_tab.权限) as 权限,
GROUP_CONCAT(DISTINCT merge_tab.角色) as 角色,
GROUP_CONCAT(DISTINCT merge_tab.岗位) as 岗位,
CASE 
    WHEN GROUP_CONCAT(DISTINCT merge_tab.是否直接关联资源组) LIKE '%是%' THEN '是'
    ELSE '否'
END as 是否直接关联资源组,
merge_tab.账号开通时间,
MIN(merge_tab.权限开通时间) as 权限开通时间
from ((
-- 通过角色关联的资源组（仅角色获得的权限）
select
usr.user_name as '姓名',
usr.employee_id as '工号',
GROUP_CONCAT(DISTINCT org.org_name) as '部门',
GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
GROUP_CONCAT(DISTINCT role.role_name) as '角色',
NULL as '岗位',
'否' as '是否直接关联资源组',
usr.create_time as '账号开通时间',
MIN(usr_role.create_time) as '权限开通时间'
from 
ac_user usr
inner join ac_user_role usr_role on usr_role.user_id = usr.id
inner join ac_role role on usr_role.role_id = role.id and role.del_flag = 0
inner join ac_role_operate_group role_og on role_og.role_id = role.id
inner join ac_operate_group og on role_og.operate_group_id = og.id
inner join ac_operate_group_url_resource og_url on og_url.operate_group_id = og.id
inner join ac_url_resource url_res on og_url.url_resource_id = url_res.id
left join ac_user_org usr_org on usr_org.user_id = usr.id
left join ac_org org on usr_org.org_id = org.id
where usr.active_status_flag = 1
and url_res.url IN ('/driverEntry/driverBackPass', '/highSeas/forceBackUpPass')
GROUP BY usr.user_name, usr.employee_id
)

UNION 

(
-- 通过岗位关联的资源组（仅岗位获得的权限）
select
usr.user_name as '姓名',
usr.employee_id as '工号',
GROUP_CONCAT(DISTINCT org.org_name) as '部门',
GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
NULL as '角色',
GROUP_CONCAT(DISTINCT position.position_name) as '岗位',
'否' as '是否直接关联资源组',
usr.create_time as '账号开通时间',
MIN(usr_position.create_time) as '权限开通时间'
from 
ac_user usr
inner join ac_user_position usr_position on usr_position.user_id = usr.id
inner join ac_position position on usr_position.position_id = position.id and position.del_flag = 0
inner join ac_position_operate_group position_og on position_og.position_id = position.id
inner join ac_operate_group og on position_og.operate_group_id = og.id
inner join ac_operate_group_url_resource og_url on og_url.operate_group_id = og.id
inner join ac_url_resource url_res on og_url.url_resource_id = url_res.id
left join ac_user_org usr_org on usr_org.user_id = usr.id
left join ac_org org on usr_org.org_id = org.id
where usr.active_status_flag = 1
and url_res.url IN ('/driverEntry/driverBackPass', '/highSeas/forceBackUpPass')
GROUP BY usr.user_name, usr.employee_id
)

UNION 

(
-- 直接关联的资源组
select
usr.user_name as '姓名',
usr.employee_id as '工号',
GROUP_CONCAT(DISTINCT org.org_name) as '部门',
GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
NULL as '角色',
NULL as '岗位',
'是' as '是否直接关联资源组',
usr.create_time as '账号开通时间',
MIN(user_og.create_time) as '权限开通时间'
from 
ac_user usr
inner join ac_user_operate_group user_og on user_og.user_id = usr.id
inner join ac_operate_group og on user_og.operate_group_id = og.id
inner join ac_operate_group_url_resource og_url on og_url.operate_group_id = og.id
inner join ac_url_resource url_res on og_url.url_resource_id = url_res.id
left join ac_user_org usr_org on usr_org.user_id = usr.id
left join ac_org org on usr_org.org_id = org.id
where usr.active_status_flag = 1
and url_res.url IN ('/driverEntry/driverBackPass', '/highSeas/forceBackUpPass')
GROUP BY usr.user_name, usr.employee_id
)) as merge_tab
GROUP BY merge_tab.姓名, merge_tab.工号
ORDER BY merge_tab.姓名 asc, merge_tab.工号 asc
LIMIT 200 OFFSET 0


select 
merge_tab.姓名,
merge_tab.工号,
GROUP_CONCAT(DISTINCT merge_tab.权限) as 权限,
merge_tab.账号开通时间,
MIN(merge_tab.权限开通时间) as 权限开通时间,
GROUP_CONCAT(DISTINCT merge_tab.城市名称) as 城市名称
from ((
-- 通过角色关联的资源组
select
usr.user_name as '姓名',
usr.employee_id as '工号',
GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
usr.create_time as '账号开通时间',
MIN(usr_role.create_time) as '权限开通时间',
city.city_name as '城市名称'
from 
ac_user usr,
ac_role role,
ac_user_role usr_role,
ac_role_operate_group role_og,
ac_operate_group og,
user_system.ac_user_city city
where 
usr.active_status_flag=1
and role.del_flag=0
and usr_role.user_id=usr.id
and usr_role.role_id=role.id
and role_og.role_id=role.id
and role_og.operate_group_id = og.id
and city.user_id = usr.id
and og.id IN (4520, 4519)
GROUP BY usr.user_name,usr.employee_id,city.city_name
ORDER by usr.user_name asc,usr.employee_id asc 
)

UNION 
(
-- 通过岗位关联的资源组
select
usr.user_name as '姓名',
usr.employee_id as '工号',
GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
usr.create_time as '账号开通时间',
MIN(usr_position.create_time) as '权限开通时间',
city.city_name as '城市名称'
from 
ac_user usr,
ac_position position,
ac_user_position usr_position,
ac_position_operate_group position_og,
ac_operate_group og,
user_system.ac_user_city city
where 
usr.active_status_flag=1
and position.del_flag=0
and usr_position.user_id=usr.id
and usr_position.position_id=position.id
and position_og.position_id=position.id
and position_og.operate_group_id = og.id
and city.user_id = usr.id
and og.id IN (4520, 4519)
GROUP BY usr.user_name,usr.employee_id,city.city_name
ORDER by usr.user_name asc,usr.employee_id asc 
)

UNION 
(
-- 直接关联的资源组
select
usr.user_name as '姓名',
usr.employee_id as '工号',
GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
usr.create_time as '账号开通时间',
MIN(user_og.create_time) as '权限开通时间',
city.city_name as '城市名称'
from 
ac_user usr,
ac_user_operate_group user_og,
ac_operate_group og,
user_system.ac_user_city city
where 
usr.active_status_flag=1
and user_og.user_id=usr.id
and user_og.operate_group_id=og.id
and city.user_id = usr.id
and og.id IN (4520, 4519)
GROUP BY usr.user_name,usr.employee_id,city.city_name
ORDER by usr.user_name asc,usr.employee_id asc 
)) as merge_tab
GROUP BY merge_tab.姓名,merge_tab.工号
LIMIT 200 OFFSET 0


SELECT
    r.role_name AS '角色名称',
    r.id AS '角色ID',
    GROUP_CONCAT(DISTINCT u.user_name ORDER BY u.user_name SEPARATOR ',') AS '拥有此角色的用户'
FROM
    ac_role r
INNER JOIN
    ac_user_role ur ON r.id = ur.role_id  -- 关联用户角色表
INNER JOIN
    ac_user u ON ur.user_id = u.id        -- 关联用户表获取用户名
INNER JOIN
    user_system.ac_user_city uc ON u.id = uc.user_id -- 关联用户城市表以筛选城市
WHERE
    r.id IN (721, 88, 1063, 125, 4261, 129, 1665, 4269, 3475, 4221, 2342, 4219, 636, 4276, 1218, 4332, 4335, 4334) -- 您指定的角色ID列表
    AND uc.city_name IN ('北京') -- 您指定的城市列表
    AND u.active_status_flag = 1  -- 用户为激活状态
    AND r.del_flag = 0            -- 角色未被删除
GROUP BY
    r.id, r.role_name             -- 按角色ID和角色名称分组
ORDER BY
    r.role_name ASC               -- 按角色名称排序


SELECT
    r.role_name AS '角色名称',
    r.id AS '角色ID',
    GROUP_CONCAT(DISTINCT u.user_name ORDER BY u.user_name SEPARATOR ',') AS '拥有此角色的用户'
FROM
    ac_role r
INNER JOIN
    ac_user_role ur ON r.id = ur.role_id  -- 关联用户角色表
INNER JOIN
    ac_user u ON ur.user_id = u.id        -- 关联用户表获取用户名
INNER JOIN
    user_system.ac_user_city uc ON u.id = uc.user_id -- 关联用户城市表以筛选城市
WHERE
    r.id IN (721, 88, 1063, 125, 4261, 129, 1665, 4269, 3475, 4221, 2342, 4219, 636, 4276, 1218, 4332, 4335, 4334) -- 您指定的角色ID列表
    AND uc.city_name IN ('上海') -- 您指定的城市列表
    AND u.active_status_flag = 1  -- 用户为激活状态
    AND r.del_flag = 0            -- 角色未被删除
GROUP BY
    r.id, r.role_name             -- 按角色ID和角色名称分组
ORDER BY
    r.role_name ASC               -- 按角色名称排序


SELECT
    r.role_name AS '角色名称',
    r.id AS '角色ID',
    GROUP_CONCAT(DISTINCT u.user_name ORDER BY u.user_name SEPARATOR ',') AS '拥有此角色的用户'
FROM
    ac_role r
INNER JOIN
    ac_user_role ur ON r.id = ur.role_id  -- 关联用户角色表
INNER JOIN
    ac_user u ON ur.user_id = u.id        -- 关联用户表获取用户名
INNER JOIN
    user_system.ac_user_city uc ON u.id = uc.user_id -- 关联用户城市表以筛选城市
WHERE
    r.id IN (721, 88, 1063, 125, 4261, 129, 1665, 4269, 3475, 4221, 2342, 4219, 636, 4276, 1218, 4332, 4335, 4334) -- 您指定的角色ID列表
    AND uc.city_name IN ('杭州') -- 您指定的城市列表
    AND u.active_status_flag = 1  -- 用户为激活状态
    AND r.del_flag = 0            -- 角色未被删除
GROUP BY
    r.id, r.role_name             -- 按角色ID和角色名称分组
ORDER BY
    r.role_name ASC               -- 按角色名称排序


SELECT
    r.role_name AS '角色名称',
    r.id AS '角色ID',
    GROUP_CONCAT(DISTINCT u.user_name ORDER BY u.user_name SEPARATOR ',') AS '拥有此角色的用户'
FROM
    ac_role r
INNER JOIN
    ac_user_role ur ON r.id = ur.role_id  -- 关联用户角色表
INNER JOIN
    ac_user u ON ur.user_id = u.id        -- 关联用户表获取用户名
INNER JOIN
    user_system.ac_user_city uc ON u.id = uc.user_id -- 关联用户城市表以筛选城市
WHERE
    r.id IN (721, 88, 1063, 125, 4261, 129, 1665, 4269, 3475, 4221, 2342, 4219, 636, 4276, 1218, 4332, 4335, 4334) -- 您指定的角色ID列表
    AND uc.city_name IN ('贵阳') -- 您指定的城市列表
    AND u.active_status_flag = 1  -- 用户为激活状态
    AND r.del_flag = 0            -- 角色未被删除
GROUP BY
    r.id, r.role_name             -- 按角色ID和角色名称分组
ORDER BY
    r.role_name ASC               -- 按角色名称排序


SELECT
    r.role_name AS '角色名称',
    r.id AS '角色ID',
    GROUP_CONCAT(DISTINCT u.user_name ORDER BY u.user_name SEPARATOR ',') AS '拥有此角色的用户'
FROM
    ac_role r
INNER JOIN
    ac_user_role ur ON r.id = ur.role_id  -- 关联用户角色表
INNER JOIN
    ac_user u ON ur.user_id = u.id        -- 关联用户表获取用户名
INNER JOIN
    user_system.ac_user_city uc ON u.id = uc.user_id -- 关联用户城市表以筛选城市
WHERE
    r.id IN (721, 88, 1063, 125, 4261, 129, 1665, 4269, 3475, 4221, 2342, 4219, 636, 4276, 1218, 4332, 4335, 4334) -- 您指定的角色ID列表
    AND uc.city_name IN ('全国') -- 您指定的城市列表
    AND u.active_status_flag = 1  -- 用户为激活状态
    AND r.del_flag = 0            -- 角色未被删除
GROUP BY
    r.id, r.role_name             -- 按角色ID和角色名称分组
ORDER BY
    r.role_name ASC               -- 按角色名称排序


SELECT
    u.id AS '用户ID',
    u.user_name AS '姓名',
    u.employee_id AS '工号',
    r.role_name AS '角色名称',
    r.id AS '角色ID',
    GROUP_CONCAT(DISTINCT org.org_name SEPARATOR ',') AS '部门',
    GROUP_CONCAT(DISTINCT CASE WHEN uc.city_code = '0000' THEN '全国' ELSE uc.city_name END SEPARATOR ',') AS '城市'
FROM
    ac_role r
INNER JOIN
    ac_user_role ur ON r.id = ur.role_id
INNER JOIN
    ac_user u ON ur.user_id = u.id
LEFT JOIN
    ac_user_org uo ON u.id = uo.user_id
LEFT JOIN
    ac_org org ON uo.org_id = org.id
LEFT JOIN
    user_system.ac_user_city uc ON u.id = uc.user_id
WHERE
    r.id = 4229                 -- 指定角色ID
    AND u.type = 1                -- 指定用户类型
    AND u.active_status_flag = 1  -- 用户为激活状态
    AND r.del_flag = 0            -- 角色未被删除
GROUP BY
    u.id, u.user_name, u.employee_id, r.role_name, r.id
ORDER BY
    u.user_name ASC;



    SELECT 
    merge_tab.姓名,
    merge_tab.工号,
    merge_tab.部门,
    merge_tab.岗位,
    GROUP_CONCAT(DISTINCT merge_tab.权限) as 权限,
    merge_tab.账号开通时间,
    MIN(merge_tab.权限开通时间) as 权限开通时间,
    merge_tab.权限结束时间,
    GROUP_CONCAT(DISTINCT merge_tab.城市) as 城市,
    CASE 
        WHEN merge_tab.active_status_flag = 0 THEN '冻结'
        WHEN merge_tab.active_status_flag = 1 THEN '启用'
        ELSE '未知'
    END as 用户状态
FROM (
    -- 通过角色关联的资源组
    SELECT
        usr.user_name as '姓名',
        usr.employee_id as '工号',
        GROUP_CONCAT(DISTINCT org.org_name) as '部门',
        usr.title as '岗位',
        GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
        usr.create_time as '账号开通时间',
        MIN(usr_role.create_time) as '权限开通时间',
        usr.active_end_time as '权限结束时间',
        GROUP_CONCAT(DISTINCT uc.city_name) as '城市',
        usr.active_status_flag
    FROM ac_user usr
    INNER JOIN ac_user_role usr_role ON usr.id = usr_role.user_id
        AND usr.type = 0
        AND (usr.create_time BETWEEN '2025-01-01' AND '2025-03-31 23:59:59'
             OR usr_role.create_time BETWEEN '2025-01-01' AND '2025-03-31 23:59:59')
    INNER JOIN ac_role role ON usr_role.role_id = role.id AND role.del_flag = 0
    INNER JOIN ac_role_operate_group role_og ON role.id = role_og.role_id
    INNER JOIN ac_operate_group og ON role_og.operate_group_id = og.id
    INNER JOIN ac_user_org usr_org ON usr.id = usr_org.user_id
    INNER JOIN ac_org org ON usr_org.org_id = org.id
    LEFT JOIN ac_user_city uc ON usr.id = uc.user_id
    GROUP BY usr.user_name, usr.employee_id, usr.active_status_flag, usr.active_end_time

    UNION ALL

    -- 通过岗位关联的资源组
    SELECT
        usr.user_name as '姓名',
        usr.employee_id as '工号',
        GROUP_CONCAT(DISTINCT org.org_name) as '部门',
        usr.title as '岗位',
        GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
        usr.create_time as '账号开通时间',
        MIN(usr_position.create_time) as '权限开通时间',
        usr.active_end_time as '权限结束时间',
        GROUP_CONCAT(DISTINCT uc.city_name) as '城市',
        usr.active_status_flag
    FROM ac_user usr
    INNER JOIN ac_user_position usr_position ON usr.id = usr_position.user_id
        AND usr.type = 0
        AND (usr.create_time BETWEEN '2025-01-01' AND '2025-03-31 23:59:59'
             OR usr_position.create_time BETWEEN '2025-01-01' AND '2025-03-31 23:59:59')
    INNER JOIN ac_position position ON usr_position.position_id = position.id AND position.del_flag = 0
    INNER JOIN ac_position_operate_group position_og ON position.id = position_og.position_id
    INNER JOIN ac_operate_group og ON position_og.operate_group_id = og.id
    INNER JOIN ac_user_org usr_org ON usr.id = usr_org.user_id
    INNER JOIN ac_org org ON usr_org.org_id = org.id
    LEFT JOIN ac_user_city uc ON usr.id = uc.user_id
    GROUP BY usr.user_name, usr.employee_id, usr.active_status_flag, usr.active_end_time

    UNION ALL

    -- 直接关联的资源组
    SELECT
        usr.user_name as '姓名',
        usr.employee_id as '工号',
        GROUP_CONCAT(DISTINCT org.org_name) as '部门',
        usr.title as '岗位',
        GROUP_CONCAT(DISTINCT og.operate_group_name) as '权限',
        usr.create_time as '账号开通时间',
        MIN(user_og.create_time) as '权限开通时间',
        usr.active_end_time as '权限结束时间',
        GROUP_CONCAT(DISTINCT uc.city_name) as '城市',
        usr.active_status_flag
    FROM ac_user usr
    INNER JOIN ac_user_operate_group user_og ON usr.id = user_og.user_id
        AND usr.type = 0
        AND (usr.create_time BETWEEN '2025-01-01' AND '2025-03-31 23:59:59'
             OR user_og.create_time BETWEEN '2025-01-01' AND '2025-03-31 23:59:59')
    INNER JOIN ac_operate_group og ON user_og.operate_group_id = og.id
    INNER JOIN ac_user_org usr_org ON usr.id = usr_org.user_id
    INNER JOIN ac_org org ON usr_org.org_id = org.id
    LEFT JOIN ac_user_city uc ON usr.id = uc.user_id
    GROUP BY usr.user_name, usr.employee_id, usr.active_status_flag, usr.active_end_time
) AS merge_tab
GROUP BY merge_tab.姓名, merge_tab.工号, merge_tab.active_status_flag, merge_tab.权限结束时间 


-- 根据用户ID查询部门完整路径（使用离线表）
-- 注意:
-- 1. 此查询为Hive/Spark SQL语法，其中的函数（如SIZE, SPLIT, COLLECT_LIST）和表名（如cc_ods.mysql__user_system__ac_oa_org）是针对特定数据仓库环境的。
-- 2. 查询中的 '20241003' 是数据分区日期，请根据需要修改为最新的日期。
-- 3. 假设 ac_user 和 ac_user_org 表在执行环境中也可用。
WITH org_levels AS (
  SELECT
    org_id,
    name,
    parent_id,
    path,
    SIZE(SPLIT(path, '\\|')) - 3 AS level
  FROM cc_ods.mysql__user_system__ac_oa_org WHERE dt = '20241003'
),
org_hierarchy AS (
  SELECT
    o1.org_id,
    CONCAT_WS('/', COLLECT_LIST(o2.name) OVER (
      PARTITION BY o1.org_id
      ORDER BY o2.level
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    )) AS path_name
  FROM org_levels o1
  JOIN org_levels o2 ON o1.path LIKE CONCAT(o2.path, '%')
),
longest_path AS (
  SELECT
    org_id,
    path_name,
    ROW_NUMBER() OVER (PARTITION BY org_id ORDER BY LENGTH(path_name) DESC) AS rn
  FROM org_hierarchy
)
SELECT
    usr.id AS `用户ID`,
    usr.user_name AS `姓名`,
    usr.employee_id AS `工号`,
    CONCAT_WS('; ', COLLECT_SET(lp.path_name)) AS `部门完整路径`
FROM
    ac_user usr
LEFT JOIN
    ac_user_org uo ON usr.id = uo.user_id
LEFT JOIN
    (SELECT org_id, path_name FROM longest_path WHERE rn = 1) lp ON uo.org_id = lp.org_id
WHERE
    usr.id = 123; -- 请在这里替换为您想查询的用户ID


-- ==========================================================================================
-- 以下是功能相同，但为 MySQL 8.0+ 设计的语法版本
-- ==========================================================================================
-- 注意:
-- 1. 此查询使用了MySQL 8.0+ 支持的CTE(WITH子句)和窗口函数。
-- 2. 假设MySQL中的组织架构表名为 `ac_oa_org`，并且用户直接有权限访问。
WITH org_levels AS (
  SELECT
    id as org_id, -- 假设MySQL表中主键为id
    name,
    parent_id,
    path,
    -- 用计算'|'数量的方式模拟 hive 的 SIZE(SPLIT(...)) 来获取层级
    (LENGTH(path) - LENGTH(REPLACE(path, '|', ''))) - 2 AS `level`
  FROM ac_oa_org
),
org_hierarchy AS (
  SELECT
    o1.org_id,
    -- MySQL 8.0+ 支持在窗口函数中使用 GROUP_CONCAT
    GROUP_CONCAT(o2.name ORDER BY o2.level SEPARATOR '/') OVER (
      PARTITION BY o1.org_id
    ) AS path_name
  FROM org_levels o1
  JOIN org_levels o2 ON o1.path LIKE CONCAT(o2.path, '%')
),
longest_path AS (
  SELECT
    org_id,
    path_name,
    ROW_NUMBER() OVER (PARTITION BY org_id ORDER BY LENGTH(path_name) DESC) AS rn
  FROM org_hierarchy
)
SELECT
    usr.id AS `用户ID`,
    usr.user_name AS `姓名`,
    usr.employee_id AS `工号`,
    lp.path_name AS `部门完整路径`
FROM
    ac_user usr
LEFT JOIN
    ac_user_org uo ON usr.id = uo.user_id
LEFT JOIN
    (SELECT org_id, path_name FROM longest_path WHERE rn = 1) lp ON uo.org_id = lp.org_id
WHERE
    usr.id = 123; -- 请在这里替换为您想查询的用户ID 


-- ==========================================================================================
-- 以下是功能相同，但为 MySQL 5.6 / 5.7 设计的兼容版本 (根据 ac_oa_user 表结构更新)
-- ==========================================================================================
-- 注意:
-- 1. 此查询不使用CTE(WITH子句)和窗口函数，以兼容旧版MySQL。
-- 2. 它通过 ac_user.employee_id 与 ac_oa_user.emp_no 关联，并处理 ac_oa_user.dep_id 中的'|'符号。
SELECT
    usr.id AS `用户ID`,
    usr.user_name AS `姓名`,
    usr.employee_id AS `工号`,
    lp.path_name AS `部门完整路径`
FROM
    ac_user usr
LEFT JOIN
    ac_oa_user oa_usr ON usr.employee_id = oa_usr.emp_no
LEFT JOIN
    (
        -- 派生表开始：计算每个部门的完整层级路径
        SELECT
            o1.org_id,
            GROUP_CONCAT(
                o2.name
                ORDER BY LENGTH(o2.path)
                SEPARATOR '/'
            ) AS path_name
        FROM
            ac_oa_org o1
        JOIN
            ac_oa_org o2 ON o1.path LIKE CONCAT(o2.path, '%')
        GROUP BY
            o1.org_id
    ) lp ON REPLACE(oa_usr.dep_id, '|', '') = lp.org_id
WHERE
    usr.id = 123; -- 请在这里替换为您想查询的用户ID 


    create table user_system.ac_user_company
(
    id          bigint unsigned auto_increment comment '自增ID，不用于业务'
        primary key,
    user_id     bigint    default 0                 not null comment '用户ID',
    company_id  bigint    default 0                 not null comment '租赁或加盟商编号',
    level       tinyint   default 1                 not null comment '租赁商或加盟商类型 1-一级 2-二级',
    create_time timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户租赁公司关联表';

create index idx_create_time
    on user_system.ac_user_company (create_time);

create index idx_org_id
    on user_system.ac_user_company (company_id);

create index idx_update_time
    on user_system.ac_user_company (update_time);


-- 根据租赁公司ID列表查询用户信息
SELECT
    usr.id AS '用户ID',
    usr.user_name AS '姓名',
    CASE 
        WHEN usr.type = 0 THEN '曹操'
        WHEN usr.type = 1 THEN '观海'
        ELSE '未知'
    END AS '用户类型',
    GROUP_CONCAT(DISTINCT org.org_name SEPARATOR ', ') AS '部门名称',
    GROUP_CONCAT(DISTINCT city.city_name SEPARATOR ', ') AS '城市名称',
    comp.company_id AS '租赁商ID'
FROM
    user_system.ac_user_company comp
INNER JOIN
    ac_user usr ON comp.user_id = usr.id
LEFT JOIN
    ac_user_org uo ON usr.id = uo.user_id
LEFT JOIN
    ac_org org ON uo.org_id = org.id
LEFT JOIN
    user_system.ac_user_city city ON usr.id = city.user_id
WHERE
    comp.company_id IN (
        3550, 6986, 10809, 12524, 3544, 3549, 3763, 8347, 13485, 14077, 3531, 3762, 3765, 3766, 3769,
        3553, 3770, 7953, 9180, 10815, 11359, 12670, 13481, 13483, 13484, 13681, 13682, 13939, 13940,
        14890, 3508, 3541, 3561, 6987, 8311, 8772, 10174, 10182, 13067, 13254, 13885, 13886, 3554,
        5297, 13475, 13930, 13931, 13932, 3539, 3542, 8101, 8107, 8405, 11444, 13478, 13479, 13480,
        3533, 3626, 3771, 11362, 13476, 14139, 14140, 3753, 4226, 4779, 10470, 14039, 14040, 14042,
        14303, 15544, 15584, 3555, 6356, 6992, 3552, 7752, 8315, 8844, 13423, 13424, 13425, 13933,
        13934, 14037, 14041, 14043, 15561, 15562, 3543, 6991, 7751, 8237, 8754, 9181, 13189, 13190,
        14305, 3547, 3556, 8205, 8215, 8251, 9267, 13391, 13393, 13394, 13395, 14038, 14306, 3545,
        6989, 7977, 10920, 13184, 13187, 3566, 7227, 8632, 13182, 13183, 13185, 13186, 13188, 3536,
        3537, 3577, 3751, 3791, 6988, 10292, 10586, 13966, 3437, 3754, 13179, 13180, 13181, 13486,
        13736, 14302, 14728, 14777, 14778, 3538, 3540, 6990, 8208, 8578, 11748, 13075, 13175, 13176,
        13487, 13947, 13949, 3546, 3629, 8207, 8250, 10459, 13854, 13945, 13948, 14304, 3548, 8450,
        10443, 12386, 13178, 13944, 14358, 14359, 14360, 14361, 14362, 14363, 14364, 14365, 14366,
        14367
    )
    AND usr.active_status_flag = 1 -- 只查询非冻结状态的用户
GROUP BY
    usr.id, usr.user_name, comp.company_id
ORDER BY
    comp.company_id, usr.user_name;

    create table user_system.ac_role
(
    id              bigint auto_increment comment '标识列'
        primary key,
    role_name       varchar(50)  default ''                not null comment '角色名称',
    logic_system_id bigint       default 0                 not null comment '逻辑系统id',
    org_id          bigint       default 0                 null comment '部门组织id',
    type            tinyint      default 0                 not null comment '角色类型(0:曹操、1:带车加盟)',
    remark          varchar(255) default ''                not null,
    del_flag        tinyint(1)   default 0                 not null comment '删除标志（0:未删除，1：已经删除）',
    create_time     timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time     timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '角色表';

create index idx_create_time
    on user_system.ac_role (create_time);

create index idx_update_time
    on user_system.ac_role (update_time);

create table user_system.ac_logic_system
(
    id                      bigint auto_increment comment '标识列'
        primary key,
    logic_system_name       varchar(50)  default ''                null comment '逻辑系统名称',
    logic_system_url        varchar(100) default ''                null comment '系统地址',
    system_url_type         tinyint      default 0                 not null comment '跳转地址类型:1-原生，2-H5',
    person_in_charge        varchar(50)  default ''                null comment '负责人',
    logic_system_key        varchar(50)  default ''                null comment '系统key值',
    logic_system_secret_key varchar(50)  default ''                null comment '秘钥',
    icon_code               varchar(50)  default ''                null comment '图标编码',
    show_order              int          default 0                 not null comment '排序字段',
    type                    tinyint      default 0                 not null comment '0-曹操中心，1-移动app',
    show_type               tinyint      default 0                 not null comment '展示类型:1-tab，2-应用中心',
    superscript_type        tinyint      default 0                 not null comment '上标类型 0-无，数据字典：运营app系统上标类型 ',
    application_tag         tinyint      default 0                 not null comment '应用标签',
    frozen_status           tinyint(1)   default 0                 not null comment '冻结状态（0：未冻结，1：已冻结）',
    del_flag                tinyint(1)   default 0                 not null comment '删除标志（0：未删除，1：删除）',
    create_time             timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time             timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    is_visible              tinyint      default 1                 not null comment '是否首页可见1、是，2、否'
)
    comment '逻辑系统表';

create index idx_create_time
    on user_system.ac_logic_system (create_time);

create index idx_system_name
    on user_system.ac_logic_system (logic_system_name);

create index idx_update_time
    on user_system.ac_logic_system (update_time);

create table user_system.ac_operate_group
(
    id                 bigint auto_increment comment '标识列'
        primary key,
    operate_group_name varchar(50)  default ''                not null comment '操作组名称',
    logic_system_id    bigint       default 0                 not null comment '逻辑系统id',
    remark             varchar(255) default ''                not null comment '备注,描述',
    del_flag           tinyint(1)   default 0                 not null comment '删除标志（0：未删除，1：已删除）',
    create_time        timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time        timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    risk_rank          tinyint(1)   default 2                 not null comment '权限风险等级（R0-极高风险 R1-高风险 R2-中风险 R3-低风险 R4-无风险）',
    operate_group_type int          default 0                 not null comment '资源组类型0，历史数据1：查看，2：编辑，3：导出，4：导入，5：其他6：审核'
)
    comment '操作组表';

create index idx_create_time
    on user_system.ac_operate_group (create_time);

create index idx_update_time
    on user_system.ac_operate_group (update_time);



-- TAB1：关联的角色
-- userId、姓名、逻辑系统名、角色ID、角色名称
SELECT 
    usr.id AS userId,
    usr.user_name AS 姓名,
    ls.logic_system_name AS 逻辑系统名,
    role.id AS 角色ID,
    role.role_name AS 角色名称
FROM ac_user usr
INNER JOIN ac_user_role usr_role ON usr.id = usr_role.user_id
INNER JOIN ac_role role ON usr_role.role_id = role.id
LEFT JOIN ac_logic_system ls ON role.logic_system_id = ls.id
WHERE usr.id IN (
    210, 2821, 10517, 19996, 24666, 31178, 
    32129, 32483, 32484, 32823, 32878, 32882
)
AND usr.active_status_flag = 1
AND role.del_flag = 0
ORDER BY usr.id, role.role_name;

-- TAB2：关联的岗位
-- userId、姓名、岗位名称
SELECT 
    usr.id AS userId,
    usr.user_name AS 姓名,
    position.position_name AS 岗位名称
FROM ac_user usr
INNER JOIN ac_user_position usr_position ON usr.id = usr_position.user_id
INNER JOIN ac_position position ON usr_position.position_id = position.id
WHERE usr.id IN (
    210, 2821, 10517, 19996, 24666, 31178, 
    32129, 32483, 32484, 32823, 32878, 32882
)
AND usr.active_status_flag = 1
AND position.del_flag = 0
ORDER BY usr.id, position.position_name;

-- TAB3：直接关联的资源组
-- userId、姓名、逻辑系统名、资源组ID、资源组名称
SELECT 
    usr.id AS userId,
    usr.user_name AS 姓名,
    ls.logic_system_name AS 逻辑系统名,
    og.id AS 资源组ID,
    og.operate_group_name AS 资源组名称
FROM ac_user usr
INNER JOIN ac_user_operate_group user_og ON usr.id = user_og.user_id
INNER JOIN ac_operate_group og ON user_og.operate_group_id = og.id
LEFT JOIN ac_logic_system ls ON og.logic_system_id = ls.id
WHERE usr.id IN (
    210, 2821, 10517, 19996, 24666, 31178, 
    32129, 32483, 32484, 32823, 32878, 32882
)
AND usr.active_status_flag = 1
ORDER BY usr.id, og.operate_group_name;


-- 查询观海全部商用户信息（包含用户ID、姓名、手机号、开通城市、关联部门、租赁商）
SELECT 
    usr.id AS '用户ID',
    usr.user_name AS '姓名',
    usr.mobile AS '手机号',
    GROUP_CONCAT(DISTINCT city.city_name SEPARATOR ', ') AS '开通城市',
    GROUP_CONCAT(DISTINCT org.org_name SEPARATOR ', ') AS '关联部门',
    '全部商' AS '租赁商'
FROM ac_user usr
INNER JOIN ac_user_company comp ON usr.id = comp.user_id
LEFT JOIN user_system.ac_user_city city ON usr.id = city.user_id
LEFT JOIN ac_user_org uo ON usr.id = uo.user_id
LEFT JOIN ac_org org ON uo.org_id = org.id
WHERE comp.company_id = 0
    AND usr.active_status_flag = 1
    AND usr.type = 1
GROUP BY usr.id, usr.user_name, usr.mobile
ORDER BY usr.user_name ASC
LIMIT 200;

