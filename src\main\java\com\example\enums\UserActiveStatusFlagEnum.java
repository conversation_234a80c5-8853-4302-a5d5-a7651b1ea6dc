package com.example.enums;

/**
 * 用户活跃状态标识枚举
 */
public enum UserActiveStatusFlagEnum {
    
    ACTIVE(1, "活跃"),
    INACTIVE(0, "不活跃"),
    LOCKED(-1, "锁定");
    
    private final Integer code;
    private final String desc;
    
    UserActiveStatusFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static String getEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserActiveStatusFlagEnum enumItem : UserActiveStatusFlagEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getDesc();
            }
        }
        return null;
    }
} 