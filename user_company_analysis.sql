-- 根据用户关联的公司数量排序，取前20个用户
SELECT 
    user_id AS '用户ID',
    COUNT(company_id) AS '关联公司数量'
FROM ac_user_company
GROUP BY user_id
ORDER BY COUNT(company_id) DESC
LIMIT 20;

-- 如果需要显示用户详细信息，可以关联ac_user表
SELECT 
    uc.user_id AS '用户ID',
    u.user_name AS '姓名',
    u.employee_id AS '工号',
    COUNT(uc.company_id) AS '关联公司数量'
FROM ac_user_company uc
LEFT JOIN ac_user u ON uc.user_id = u.id
GROUP BY uc.user_id, u.user_name, u.employee_id
ORDER BY COUNT(uc.company_id) DESC
LIMIT 20;

-- 如果您想要的是公司关联的用户数量排序（每个公司关联了多少用户）
SELECT 
    company_id AS '公司ID',
    COUNT(user_id) AS '关联用户数量'
FROM ac_user_company
GROUP BY company_id
ORDER BY COUNT(user_id) DESC
LIMIT 20;

-- 更详细的公司关联用户数量查询（如果有公司信息表的话）
SELECT 
    uc.company_id AS '公司ID',
    COUNT(uc.user_id) AS '关联用户数量',
    -- 如果有公司信息表，可以添加公司名称
    -- c.company_name AS '公司名称'
    GROUP_CONCAT(DISTINCT u.user_name ORDER BY u.user_name SEPARATOR ', ') AS '关联用户列表'
FROM ac_user_company uc
LEFT JOIN ac_user u ON uc.user_id = u.id
-- LEFT JOIN company_table c ON uc.company_id = c.id  -- 如果有公司表
GROUP BY uc.company_id
ORDER BY COUNT(uc.user_id) DESC
LIMIT 20; 