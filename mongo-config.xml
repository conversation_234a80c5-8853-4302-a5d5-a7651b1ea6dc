<mongo:mongo-client id="mongoClient" credentials="${mongo.credentials}"
                    host="${mongo.hosts}" 
                    replica-set="${mongo.replicaSet:}">
    <mongo:client-options connections-per-host="${mongo.connectionsPerHost}"
                          threads-allowed-to-block-for-connection-multiplier="${mongo.threadsAllowedToBlockForConnectionMultiplier}"
                          connect-timeout="${mongo.connectTimeout}"
                          max-wait-time="${mongo.maxWaitTime}"
                          socket-keep-alive="${mongo.socketKeepAlive}"
                          socket-timeout="${mongo.socketTimeout}"/>
</mongo:mongo-client>

<mongo:mongo-client id="newMongoClient" credentials="${mongo.new.credentials}"
                    host="${mongo.new.hosts}" 
                    replica-set="${mongo.new.replicaSet:}">
    <mongo:client-options connections-per-host="${mongo.new.connectionsPerHost}"
                          threads-allowed-to-block-for-connection-multiplier="${mongo.new.threadsAllowedToBlockForConnectionMultiplier}"
                          connect-timeout="${mongo.new.connectTimeout}"
                          max-wait-time="${mongo.new.maxWaitTime}"
                          socket-keep-alive="${mongo.new.socketKeepAlive}"
                          socket-timeout="${mongo.new.socketTimeout}"/>
</mongo:mongo-client>

<bean id="mongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
    <constructor-arg name="mongo" ref="mongoClient"/>
    <constructor-arg name="databaseName" value="${mongo.dbname}"/>
</bean>

<bean id="newMongoTemplate" class="org.springframework.data.mongodb.core.MongoTemplate">
    <constructor-arg name="mongo" ref="newMongoClient"/>
    <constructor-arg name="databaseName" value="${mongo.new.dbname}"/>
</bean> 