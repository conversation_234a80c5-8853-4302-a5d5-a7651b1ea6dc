// 更新后的API接口定义
api RpcResult<PageListResult<UserDTO>> findUserByRoleIdWithPage(PageQueryCondition<UserRoleQueryDTO> queryCondition);

// 定义查询DTO类
public class UserRoleQueryDTO {
    private Long roleId;
    private Long companyId;
    
    // getter and setter
    public Long getRoleId() {
        return roleId;
    }
    
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
    
    public Long getCompanyId() {
        return companyId;
    }
    
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}

// 实现方法
public RpcResult<PageListResult<UserDTO>> findUserByRoleIdWithPage(PageQueryCondition<UserRoleQueryDTO> queryCondition) {
    UserRoleQueryDTO queryDTO = queryCondition.getData();
    PreconditionUtil.checkNotNull(queryDTO, "查询条件为空");
    PreconditionUtil.checkNotNull(queryDTO.getRoleId(), "角色编号为空");
    Long roleId = queryDTO.getRoleId();
    Long companyId = queryDTO.getCompanyId();

    RpcResult<PageListResult<UserDTO>> rpcResult = new RpcResult();
    try {
        com.caocao.bss.authserver.common.model.PageQueryCondition pageQueryCondition = BeanUtil.copyProperties(queryCondition, com.caocao.bss.authserver.common.model.PageQueryCondition.class);
        Page page = PageHelperAdaptor.preparePage(pageQueryCondition);

        // 查询数据
        List<AcUserBO> acUserBOS = acUserService.findUsersByRoleIdAndCompanyIdWithPage(roleId, companyId);
        // 设置城市信息
        acUserService.setCitysByUserIdList(acUserBOS);

        // 组装返回结果
        List<UserDTO> userDTOS = acUserBOS.stream()
                .filter(acUserBO -> !Objects.isNull(acUserBO) &&
                        Objects.equals(acUserBO.getActiveStatusFlag(), CommonConstants.YES))
                .map(acUserBO -> {
                    UserDTO acUserDTO = new UserDTO();
                    acUserDTO.setId(acUserBO.getId());
                    acUserDTO.setUserName(acUserBO.getUserName());
                    acUserDTO.setEmployeeId(acUserBO.getEmployeeId());
                    acUserDTO.setTitle(acUserBO.getTitle());
                    acUserDTO.setPhone(acUserBO.getPhone());
                    acUserDTO.setEmail(acUserBO.getEmail());
                    acUserDTO.setType(acUserBO.getType());
                    if (acUserBO.getCitys() != null) {
                        acUserDTO.setCitys(acUserBO.getCitys());
                    }
                    return acUserDTO;
                }).collect(Collectors.toList());

        PageListResult<UserDTO> pageListResult = new PageListResult<>(userDTOS);
        pageListResult.setTotal(new Long(page.getTotal()).intValue());
        pageListResult.setPageNum(pageQueryCondition.getPageNum());
        pageListResult.setPageSize(pageQueryCondition.getPageSize());

        log.info("分页查询角色关联的用户列表，角色编号={}, 公司编号={}, 页码={}, 每页数量={}, 总数={}, 返回数量={}",
                roleId, companyId, queryCondition.getPageNum(), queryCondition.getPageSize(), page.getTotal(), userDTOS.size());

        return rpcResult.success(pageListResult);
    } catch (BizException e) {
        log.warn("分页查询角色关联用户已知异常，roleId={}, companyId={}", roleId, companyId, e);
        return rpcResult.failure(e.getErrorCode(), e.getMessage());
    } catch (Exception e) {
        log.error("分页查询角色关联用户未知异常，roleId={}, companyId={}", roleId, companyId, e);
        return rpcResult.failure(ErrorCodes.OTHER_EXCEPTION.getCode(), ErrorCodes.OTHER_EXCEPTION.getMessage());
    }
}

// 服务接口定义
List<AcUserBO> findUsersByRoleIdAndCompanyIdWithPage(Long roleId, Long companyId);

// 服务实现
public List<AcUserBO> findUsersByRoleIdAndCompanyIdWithPage(Long roleId, Long companyId) {
    PreconditionUtil.checkArgument(!(null == roleId && null == companyId), ErrorCodes.DEFAULT_CHECK_COMMON_ERROR,
            "角色编号和公司编号不能同时为空");

    List<AcUserDO> result = acUserDAO.findUsersByRoleIdAndCompanyIdWithPage(roleId, companyId);

    return AcUserTransfer.toBOList(result);
}

// SQL映射定义
<select id="findUsersByRoleIdAndCompanyIdWithPage" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List_DO"/>
    FROM ac_user u
    <if test="roleId != null">
    INNER JOIN ac_user_role ur ON u.id = ur.user_id
    </if>
    <if test="companyId != null">
    INNER JOIN ac_user_company uc ON u.id = uc.user_id
    </if>
    WHERE 1=1
    <if test="roleId != null">
    AND ur.role_id = #{roleId,jdbcType=BIGINT}
    </if>
    <if test="companyId != null">
    AND uc.company_id = #{companyId,jdbcType=BIGINT}
    </if>
</select> 