SELECT COUNT(*) AS 总条数
FROM (
    SELECT 
        u.id AS 用户ID,
        u.user_name AS 姓名,
        GROUP_CONCAT(DISTINCT o.org_name) AS 部门名称,
        GROUP_CONCAT(DISTINCT 
            CASE 
                WHEN COALESCE(uc.city_code, '0000') = '0000' THEN '全国'
                ELSE COALESCE(uc.city_name, '全国')
            END
        ) AS 城市名称,
        COALESCE(GROUP_CONCAT(DISTINCT ucomp.company_id), '-1') AS 租赁商ID
    FROM user_system.ac_user u
    LEFT JOIN user_system.ac_user_city uc ON u.id = uc.user_id
    LEFT JOIN user_system.ac_user_company ucomp ON u.id = ucomp.user_id
    LEFT JOIN user_system.ac_user_org uo ON u.id = uo.user_id AND uo.admin_flag = 1
    LEFT JOIN user_system.ac_org o ON uo.org_id = o.id
    WHERE u.type = 1                    -- 观海用户
      AND u.active_status_flag = 1      -- 激活状态
      AND u.del_flag = 0                -- 未删除
      AND EXISTS (
          SELECT 1 
          FROM user_system.ac_user_org uo2 
          WHERE uo2.user_id = u.id 
            AND uo2.admin_flag = 1
      )                                 -- 至少是一个部门的管理员
    GROUP BY u.id, u.user_name
) AS count_query; 