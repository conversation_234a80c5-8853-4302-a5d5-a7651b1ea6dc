package com.example.service;

import com.example.constants.CommonConstants;
import com.example.dao.AcUserDAO;
import com.example.entity.AcUserBO;
import com.example.entity.AcUserDO;
import com.example.entity.OperateLog;
import com.example.enums.OuterNetAccessFlagEnum;
import com.example.enums.SystemLogEventEnum;
import com.example.enums.UserActiveStatusFlagEnum;
import com.example.util.PreconditionUtil;
import com.example.component.OperationLogComponent;
import com.example.util.SyncUserInfoUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserService {

    @Autowired
    private AcUserDAO acUserDAO;

    @Autowired
    private SyncUserInfoUtil syncUserInfoUtil;

    /**
     * 更新用户状态
     * @param acUserBO 用户信息
     * @param operaterId 操作人ID
     * @param logEventEnum 日志事件枚举
     */
    public void updateStatus(AcUserBO acUserBO, Long operaterId, SystemLogEventEnum logEventEnum) {
        Long userId = acUserBO.getId();
        AcUserDO acUserDO = acUserDAO.findByPrimaryKey(userId);

        PreconditionUtil.checkArgument(acUserDO != null, "用户不存在");

        //step 1. 更新
        update(acUserBO);

        //step 2. 同步状态
        syncUserInfoUtil.syncUserInfoWithId(userId);

        //step 3. 插入操作记录表
        String oldRemark = "";
        String remark = "";
        
        // 根据logEventEnum类型判断处理方式
        if (logEventEnum == SystemLogEventEnum.USER_MANAGE_UPDATE_STATUS_BY_DING_TALK) {
            // 钉钉流程更新外网访问标识
            oldRemark = OuterNetAccessFlagEnum.getDescByCode(acUserDO.getOuterNetAccessFlag());
            remark = OuterNetAccessFlagEnum.getDescByCode(acUserBO.getOuterNetAccessFlag());
        } else if (logEventEnum == SystemLogEventEnum.USER_MANAGE_UPDATE_STATUS) {
            // 普通更新用户活跃状态
            oldRemark = UserActiveStatusFlagEnum.getEnumByCode(acUserDO.getActiveStatusFlag());
            remark = UserActiveStatusFlagEnum.getEnumByCode(acUserBO.getActiveStatusFlag());
        }
        
        OperateLog operateLog = OperateLog
            .builderObj(acUserBO.getId(), operaterId, logEventEnum, oldRemark, remark);
        OperationLogComponent.post(operateLog);
    }
    
    /**
     * 更新用户信息
     * @param acUserBO 用户信息
     */
    public void update(AcUserBO acUserBO) {
        // 实现更新逻辑
        acUserDAO.update(acUserBO);
    }
    
    /**
     * 根据主键获取用户
     * @param userId 用户ID
     * @return 用户信息
     */
    public AcUserBO findByPrimaryKey(Long userId) {
        AcUserDO acUserDO = acUserDAO.findByPrimaryKey(userId);
        if (acUserDO == null) {
            return null;
        }
        // 转换逻辑...
        return convertToBO(acUserDO);
    }
    
    /**
     * DO转换为BO
     * @param acUserDO 用户DO
     * @return 用户BO
     */
    private AcUserBO convertToBO(AcUserDO acUserDO) {
        // 实现DO到BO的转换逻辑
        AcUserBO acUserBO = new AcUserBO();
        // 设置属性...
        return acUserBO;
    }
} 