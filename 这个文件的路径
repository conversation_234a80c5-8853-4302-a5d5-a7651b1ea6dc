public boolean processEvent(GenericOpenDingTalkEvent event) {
        //事件唯一Id
        String eventId = event.getEventId();
        //事件类型
        String eventType = event.getEventType();
        //事件产生时间
        Long bornTime = event.getEventBornTime();
        //获取事件体
        JSONObject eventData = event.getData();

        String processKey = getProcessKey(eventId);
        boolean processResult = false;
        try {
            // 防止多pod下重复处理
            String value = stringRedisTemplate.opsForValue().get(processKey);
            if (StringUtils.isNotEmpty(value)) {
                log.warn("processEvent 其他pod正在处理, event:{}", JSON.toJSONString(event));
                return false;
            }
            stringRedisTemplate.opsForValue().set(processKey, "1", 3000, TimeUnit.MILLISECONDS);
            DingTalkProcessInstanceEventData bizData = JSON.parseObject(JSON.toJSONString(eventData), DingTalkProcessInstanceEventData.class);

            String processInstanceId = bizData.getProcessInstanceId();
            if (StringUtils.isEmpty(processInstanceId)) {
                log.warn("钉钉processInstanceId 为空，event:{}", JSON.toJSONString(event));
                return false;
            }

            boolean processFinish = bizData.isProcessFinish();
            if (!processFinish) {
                log.info("审批状态未结束， event:{}", JSON.toJSONString(event));
                return true;
            }

            // 判断是否审批通过
            boolean agree = bizData.hasAgree();
            if (!agree) {
                log.info("审批未通过, event:{}", JSON.toJSONString(event));
                return true;
            }

            // 查询审批记录
            AcAuthApplyRecordBO applyRecordBO = acAuthApplyRecordService.selectAuthApplyDetail(processInstanceId);
            if (Objects.isNull(applyRecordBO)) {
                log.warn("查询审批详情为空，processInstanceId:{},event:{}", processInstanceId, JSON.toJSONString(event));
                return false;
            }

            // 用户id，申请权限的曹操中心用户id
            Long userId = applyRecordBO.getUserId();
            // 权限申请类型
            Integer applyType = applyRecordBO.getApplyType();
            StringBuilder errorMessages = new StringBuilder();
            
            if (Objects.equals(AuthApplyTypeEnum.OPERATE_GROUP_AUTH_APPLY.getCode(), applyType)) {
                // 给用户分配资源组权限
                processResult = doAssignUserOperateGroupPermissions(bizData, userId, applyRecordBO.getId());
                // 发送曹操中心站内信给用户
                sendWebsiteMessage(userId, processInstanceId, errorMessages);

            } else if (Objects.equals(AuthApplyTypeEnum.CITY_AUTH_APPLY.getCode(), applyType)) {
                // 给用户分配城市权限
                doAssignUserCityPermissions(applyRecordBO.getId(), userId);
                processResult = true;

            } else if (Objects.equals(AuthApplyTypeEnum.POSITION_AUTH_APPLY.getCode(), applyType)) {
                // 给用户分配岗位权限
                processResult = doAssignUserPositionPermissions(bizData, userId, applyRecordBO, errorMessages);
                // 发送曹操中心站内信给用户
                sendWebsiteMessage(userId, processInstanceId, errorMessages);
                
            } else if (Objects.equals(AuthApplyTypeEnum.POSITION_PERMISSION_MODIFY.getCode(), applyType)) {
                // 岗位权限修改
                processResult = doPositionModify(bizData, userId, applyRecordBO, errorMessages);
                // 发送曹操中心站内信给用户
                sendWebsiteMessage(userId, processInstanceId, errorMessages);
                
            } else if (Objects.equals(AuthApplyTypeEnum.USER_ACTIVE_STATUS_AUTH_APPLY.getCode(), applyType)) {
                // 用户解冻
                processResult = doActiveStatus(bizData, userId, applyRecordBO, errorMessages);
                
            } else if (Objects.equals(AuthApplyTypeEnum.USER_ACTIVE_END_TIME.getCode(), applyType)) {
                // 用户延长有效期
                processResult = doActiveEndTime(bizData, userId, applyRecordBO, errorMessages);
                
            } else if (Objects.equals(AuthApplyTypeEnum.USER_OUTER_NET_ACCESS_AUTH_APPLY.getCode(), applyType)) {
                // 开启移动端外网权限
                processResult = doOuterNetAccess(bizData, userId, applyRecordBO, errorMessages);
                
            } else {
                // 不合法的applyType
                log.error("[processEvent] 不合法的applyType applyRecordBO:{}", JSON.toJSONString(applyRecordBO));
                processResult = false;
            }

            // 无论成功失败都更新申请记录
            boolean updateResult = updateApplyRecord(event, applyRecordBO.getId());
            // 如果更新失败且原处理成功，则记录警告日志
            if (!updateResult && processResult) {
                log.warn("处理成功但更新记录失败，event:{}, applyRecordId:{}", JSON.toJSONString(event), applyRecordBO.getId());
            }
            
            return processResult;
        } catch (Exception e) {
            log.error("processEvent error, event:{}", JSON.toJSONString(event), e);
            return false;
        } finally {
            stringRedisTemplate.delete(processKey);
        }
    } 