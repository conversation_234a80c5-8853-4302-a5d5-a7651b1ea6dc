"DubboServerHandler-172.18.82.42:7071-thread-2" Id=103 BLOCKED on java.lang.Class@4375ccc6 owned by "DubboServerHandler-172.18.82.42:7071-thread-18" Id=141
	at org.geotools.referencing.factory.ReferencingFactoryContainer.instance(ReferencingFactoryContainer.java:155)
	-  blocked on java.lang.Class@4375ccc6
	at org.geotools.referencing.factory.DirectAuthorityFactory.<init>(DirectAuthorityFactory.java:84)
	at org.geotools.referencing.factory.epsg.DirectEpsgFactory.<init>(DirectEpsgFactory.java:517)
	at org.geotools.referencing.factory.epsg.FactoryUsingSQL.<init>(FactoryUsingSQL.java:51)
	at org.geotools.referencing.factory.epsg.FactoryUsingAnsiSQL.<init>(FactoryUsingAnsiSQL.java:120)
	at org.geotools.referencing.factory.epsg.hsql.FactoryUsingHSQL.<init>(FactoryUsingHSQL.java:51)
	at org.geotools.referencing.factory.epsg.hsql.ThreadedHsqlEpsgFactory$1.<init>(ThreadedHsqlEpsgFactory.java:319)
	at org.geotools.referencing.factory.epsg.hsql.ThreadedHsqlEpsgFactory.createBackingStore(ThreadedHsqlEpsgFactory.java:319)
	at org.geotools.referencing.factory.epsg.ThreadedEpsgFactory.createBackingStore0(ThreadedEpsgFactory.java:395)
	at org.geotools.referencing.factory.epsg.ThreadedEpsgFactory.createBackingStore(ThreadedEpsgFactory.java:436)
	at org.geotools.referencing.factory.DeferredAuthorityFactory.getBackingStore(DeferredAuthorityFactory.java:114)
	-  locked org.geotools.referencing.factory.epsg.hsql.ThreadedHsqlEpsgFactory@3cccffe
	at org.geotools.referencing.factory.BufferedAuthorityFactory.isAvailable(BufferedAuthorityFactory.java:218)
	at org.geotools.referencing.factory.DeferredAuthorityFactory.isAvailable(DeferredAuthorityFactory.java:100)
	at org.geotools.util.factory.FactoryRegistry.isAvailable(FactoryRegistry.java:746)
	at org.geotools.util.factory.FactoryRegistry.isAcceptable(FactoryRegistry.java:569)
	at org.geotools.util.factory.FactoryRegistry.lambda$getFactories$0(FactoryRegistry.java:253)
	at org.geotools.util.factory.FactoryRegistry$$Lambda$703/1487378418.test(Unknown Source)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.Spliterators$IteratorSpliterator.tryAdvance(Spliterators.java:1812)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.lambda$initPartialTraversalState$0(StreamSpliterators.java:294)
	at java.util.stream.StreamSpliterators$WrappingSpliterator$$Lambda$184/877785117.getAsBoolean(Unknown Source)
	at java.util.stream.StreamSpliterators$AbstractWrappingSpliterator.fillBuffer(StreamSpliterators.java:206)
	at java.util.stream.StreamSpliterators$AbstractWrappingSpliterator.doAdvance(StreamSpliterators.java:161)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.tryAdvance(StreamSpliterators.java:300)
	at java.util.Spliterators$1Adapter.hasNext(Spliterators.java:681)
	at org.geotools.util.LazySet.size(LazySet.java:89)
	at java.util.AbstractCollection.toArray(AbstractCollection.java:136)
	at java.util.ArrayList.<init>(ArrayList.java:178)
	at org.geotools.referencing.DefaultAuthorityFactory.getBackingFactory(DefaultAuthorityFactory.java:94)
	at org.geotools.referencing.DefaultAuthorityFactory.<init>(DefaultAuthorityFactory.java:67)
	at org.geotools.referencing.CRS.getAuthorityFactory(CRS.java:239)
	-  locked java.lang.Class@37f55777
	at org.geotools.referencing.CRS.decode(CRS.java:517)
	...

	Number of locked synchronizers = 1
	- java.util.concurrent.ThreadPoolExecutor$Worker@2e1ebc1b
	
"DubboServerHandler-172.18.82.42:7071-thread-18" Id=141 BLOCKED on org.geotools.referencing.factory.epsg.hsql.ThreadedHsqlEpsgFactory@3cccffe owned by "DubboServerHandler-172.18.82.42:7071-thread-2" Id=103
	at org.geotools.referencing.factory.DeferredAuthorityFactory.getBackingStore(DeferredAuthorityFactory.java:113)
	-  blocked on org.geotools.referencing.factory.epsg.hsql.ThreadedHsqlEpsgFactory@3cccffe
	at org.geotools.referencing.factory.BufferedAuthorityFactory.isAvailable(BufferedAuthorityFactory.java:218)
	at org.geotools.referencing.factory.DeferredAuthorityFactory.isAvailable(DeferredAuthorityFactory.java:100)
	at org.geotools.util.factory.FactoryRegistry.isAvailable(FactoryRegistry.java:746)
	at org.geotools.util.factory.FactoryRegistry.isAcceptable(FactoryRegistry.java:569)
	at org.geotools.util.factory.FactoryRegistry.lambda$getFactoryImplementation$2(FactoryRegistry.java:502)
	at org.geotools.util.factory.FactoryRegistry$$Lambda$699/873823025.test(Unknown Source)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.Spliterators$IteratorSpliterator.tryAdvance(Spliterators.java:1812)
	at java.util.stream.ReferencePipeline.forEachWithCancel(ReferencePipeline.java:126)
	at java.util.stream.AbstractPipeline.copyIntoWithCancel(AbstractPipeline.java:498)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:485)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.FindOps$FindOp.evaluateSequential(FindOps.java:152)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.findFirst(ReferencePipeline.java:464)
	at org.geotools.util.factory.FactoryRegistry.getFactoryImplementation(FactoryRegistry.java:503)
	at org.geotools.util.factory.FactoryRegistry.getFactory(FactoryRegistry.java:420)
	at org.geotools.util.factory.FactoryCreator.getFactory(FactoryCreator.java:138)
	at org.geotools.referencing.ReferencingFactoryFinder.getAuthorityFactory(ReferencingFactoryFinder.java:223)
	-  locked java.lang.Class@4375ccc6
	at org.geotools.referencing.ReferencingFactoryFinder.getCoordinateOperationAuthorityFactory(ReferencingFactoryFinder.java:471)
	at org.geotools.referencing.operation.AuthorityBackedFactory.getAuthorityFactory(AuthorityBackedFactory.java:168)
	at org.geotools.referencing.operation.AuthorityBackedFactory.isAvailable(AuthorityBackedFactory.java:531)
	at org.geotools.util.factory.FactoryRegistry.isAvailable(FactoryRegistry.java:746)
	at org.geotools.util.factory.FactoryRegistry.isAcceptable(FactoryRegistry.java:569)
	at org.geotools.util.factory.FactoryRegistry.lambda$getFactories$0(FactoryRegistry.java:253)
	at org.geotools.util.factory.FactoryRegistry$$Lambda$703/1487378418.test(Unknown Source)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.Spliterators$IteratorSpliterator.tryAdvance(Spliterators.java:1812)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.lambda$initPartialTraversalState$0(StreamSpliterators.java:294)
	at java.util.stream.StreamSpliterators$WrappingSpliterator$$Lambda$184/877785117.getAsBoolean(Unknown Source)
	...

	Number of locked synchronizers = 1
	- java.util.concurrent.ThreadPoolExecutor$Worker@3671c3b6