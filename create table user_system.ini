create table user_system.ac_operate_group
(
    id                 bigint auto_increment comment '标识列'
        primary key,
    operate_group_name varchar(50)  default ''                not null comment '操作组名称',
    logic_system_id    bigint       default 0                 not null comment '逻辑系统id',
    remark             varchar(255) default ''                not null comment '备注,描述',
    del_flag           tinyint(1)   default 0                 not null comment '删除标志（0：未删除，1：已删除）',
    create_time        timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time        timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    risk_rank          tinyint(1)   default 2                 not null comment '权限风险等级（R0-极高风险 R1-高风险 R2-中风险 R3-低风险 R4-无风险）',
    operate_group_type int          default 0                 not null comment '资源组类型0，历史数据1：查看，2：编辑，3：导出，4：导入，5：其他6：审核'
)
    comment '操作组表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_operate_group (create_time);

create index idx_update_time
    on user_system.ac_operate_group (update_time);

create table user_system.ac_operate_group_url_resource
(
    id               bigint unsigned auto_increment
        primary key,
    operate_group_id bigint unsigned default '0'               not null comment '操作组id',
    url_resource_id  bigint unsigned default '0'               not null comment 'ur资源id',
    create_time      timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time      timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint idx_group_url
        unique (operate_group_id, url_resource_id)
)
    comment '操作组资源关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_operate_group_url_resource (create_time);

create index idx_update_time
    on user_system.ac_operate_group_url_resource (update_time);

create table user_system.ac_org
(
    id           bigint auto_increment comment '标识列'
        primary key,
    pid          bigint       default 0                 null comment '父结点id',
    geely_org_no bigint       default 0                 not null comment '吉利组织机构编号',
    org_name     varchar(50)  default ''                not null comment '组织名称',
    org_no       varchar(100) default ''                not null comment '部门编号',
    type         tinyint      default 1                 not null comment '0总部，1其他',
    del_flag     tinyint(1)   default 0                 not null comment '删除标志（0：未删除，1：已删除）',
    create_time  timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time  timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    oa_org_no    bigint       default 0                 not null comment 'oa接口返回的部门编号'
)
    comment '部门组织表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_org (create_time);

create index idx_oa_org_no
    on user_system.ac_org (oa_org_no);

create index idx_update_time
    on user_system.ac_org (update_time);

create table user_system.ac_org_system
(
    id           bigint unsigned auto_increment comment '自增ID，不用于业务'
        primary key,
    logic_system bigint    default 0                 not null comment '逻辑系统iID',
    org_id       bigint    default 0                 not null comment '部门ID',
    del_flag     tinyint   default 0                 not null comment '是否部门管理员(0：不是，1：是)',
    create_time  timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time  timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '部门系统关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_org_system (create_time);

create index idx_logic_system
    on user_system.ac_org_system (logic_system);

create index idx_org_id
    on user_system.ac_org_system (org_id);

create index idx_update_time
    on user_system.ac_org_system (update_time);

create table user_system.ac_physics_system
(
    id                  bigint auto_increment
        primary key,
    name                varchar(50)  default ''                null comment '名称',
    physics_system_name varchar(50)  default ''                not null comment '物理系统名称',
    host                varchar(255)                           null,
    gray_host           varchar(255) default ''                not null comment '灰度host',
    del_flag            tinyint(1)   default 0                 not null comment '删除标志（0：未删除，1：已删除）',
    create_time         timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time         timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '物理系统表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_physics_system (create_time);

create index idx_physics_name
    on user_system.ac_physics_system (physics_system_name);

create index idx_update_time
    on user_system.ac_physics_system (update_time);

create table user_system.ac_position
(
    id            bigint unsigned auto_increment comment '标识列'
        primary key,
    position_name varchar(50)  default ''                not null comment '岗位名称',
    remark        varchar(255) default ''                null comment '备注信息',
    del_flag      tinyint(1)   default 0                 not null comment '删除标志（0:未删除，1：已经删除）',
    create_time   timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time   timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '授权系统-岗位表' engine = InnoDB
                              charset = utf8mb4;

create index idx_create_time
    on user_system.ac_position (create_time);

create index idx_update_time
    on user_system.ac_position (update_time);

create table user_system.ac_position_operate_group
(
    id               bigint unsigned auto_increment comment '标识列'
        primary key,
    position_id      bigint    default 0                 not null comment '岗位id',
    operate_group_id bigint    default 0                 not null comment '操作组id',
    create_time      timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time      timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '授权系统-岗位资源组关联表' engine = InnoDB
                                        charset = utf8mb4;

create index idx_create_time
    on user_system.ac_position_operate_group (create_time);

create index idx_operate_group_id
    on user_system.ac_position_operate_group (operate_group_id);

create index idx_position_id
    on user_system.ac_position_operate_group (position_id);

create index idx_update_time
    on user_system.ac_position_operate_group (update_time);

create table user_system.ac_role
(
    id              bigint auto_increment comment '标识列'
        primary key,
    role_name       varchar(50)  default ''                not null comment '角色名称',
    logic_system_id bigint       default 0                 not null comment '逻辑系统id',
    org_id          bigint       default 0                 null comment '部门组织id',
    type            tinyint      default 0                 not null comment '角色类型(0:曹操、1:带车加盟)',
    remark          varchar(255) default ''                not null,
    del_flag        tinyint(1)   default 0                 not null comment '删除标志（0:未删除，1：已经删除）',
    create_time     timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time     timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '角色表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_role (create_time);

create index idx_update_time
    on user_system.ac_role (update_time);

create table user_system.ac_role_operate_group
(
    id               bigint auto_increment comment '标识列'
        primary key,
    role_id          bigint    default 0                 not null comment '角色id',
    operate_group_id bigint    default 0                 not null comment '操作组id',
    create_time      timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time      timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '角色操作组关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_role_operate_group (create_time);

create index idx_operate_group_id
    on user_system.ac_role_operate_group (operate_group_id);

create index idx_roleI_id
    on user_system.ac_role_operate_group (role_id);

create index idx_update_time
    on user_system.ac_role_operate_group (update_time);

create table user_system.ac_role_url_detail
(
    id            bigint unsigned auto_increment
        primary key,
    role_id       bigint unsigned default '0'               not null comment '角色id',
    url_detail_id bigint          default 0                 not null comment '脱敏资源id',
    is_visible    tinyint(1)      default 1                 not null comment '是否可见 0不可见，1可见',
    is_sensitive  tinyint(1)      default 1                 not null comment '是否脱敏 0 否，1 是',
    remark        varchar(50)     default ''                null comment '备注',
    create_time   timestamp       default CURRENT_TIMESTAMP not null,
    update_time   timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '角色脱敏资源关系表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_role_url_detail (create_time);

create index idx_update_time
    on user_system.ac_role_url_detail (update_time);

create table user_system.ac_url_resource
(
    id                bigint auto_increment comment '标识列'
        primary key,
    pid               bigint           default 0                 not null comment '上级id',
    logic_system_id   bigint           default 0                 not null comment '逻辑系统id',
    physics_system_id bigint           default 0                 not null comment '物理系统id',
    url_name          varchar(50)      default ''                not null comment 'url 名称',
    url               varchar(255)     default ''                not null comment '资源路径',
    url_type          tinyint unsigned default '1'               not null comment '1.菜单 ，2.功能(如 增删改详情等)',
    show_order        int              default 0                 not null comment '排序字段',
    is_special        tinyint unsigned default '0'               not null comment '是否特殊操作(如兑换码等敏感操作) 0，否；1是',
    url_scope         tinyint unsigned default '2'               not null comment '资源范围 ， 0 无需登陆就可访问，1 登陆就有的 ，2 需授权的',
    is_new_open       tinyint unsigned default '0'               not null comment '是否新开页面 0 否， 1 是（如运营地图、实时报表等）',
    is_visible        tinyint unsigned default '1'               not null comment '是否可见 0 不可见 1可见',
    menu_route_id     bigint           default 0                 not null comment '菜单路由id',
    del_flag          tinyint(1)       default 0                 not null comment '删除标志（0：未删除，1：已删除）',
    create_time       timestamp        default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time       timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    extend_content    varchar(512)     default ''                null comment '扩展字段, 如菜单帮助文档ID',
    is_notice         tinyint          default 0                 not null comment '是否需要通知，1：需要通知，0：不需要通知',
    tip_param         varchar(512)     default ''                null comment '配置参数'
)
    comment 'url资源表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_url_resource (create_time);

create index idx_physics_url_scope
    on user_system.ac_url_resource (physics_system_id, url_scope);

create index idx_update_time
    on user_system.ac_url_resource (update_time);

create index idx_url_physics_id
    on user_system.ac_url_resource (url, physics_system_id);

create table user_system.ac_url_resource_detail
(
    id                bigint unsigned auto_increment comment '主键'
        primary key,
    url_resource_id   bigint unsigned  default '0'               not null comment '资源id',
    class_name        varchar(80)      default ''                not null comment '实体类全类名称',
    model_col_name    varchar(20)      default ''                not null comment '列名称',
    model_col_name_cn varchar(50)      default ''                not null comment '列中文描述',
    detail_type       tinyint(1)       default 0                 not null comment '脱敏类型，0 电话，1 身份证 ，2 银行卡',
    del_flag          tinyint unsigned default '0'               not null comment '0 未删除，1 已删除',
    create_time       timestamp        default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time       timestamp        default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '脱敏资源表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_url_resource_detail (create_time);

create index idx_update_time
    on user_system.ac_url_resource_detail (update_time);

create table user_system.ac_user
(
    id                    bigint auto_increment comment '标识列'
        primary key,
    user_name             varchar(50)  default ''                null comment '用户名',
    pwd                   varchar(50)  default ''                null comment '密码',
    email                 varchar(100) default ''                null comment '电子邮箱',
    phone                 varchar(20)  default ''                null comment '手机号',
    type                  tinyint      default 0                 not null comment '用户类型(0:曹操、1:观海)',
    employee_id           varchar(20)  default ''                not null comment '工号',
    company_id            bigint       default 0                 not null comment '公司Id',
    title                 varchar(50)                            null comment '职位',
    active_status_flag    tinyint(1)   default 0                 not null comment '是否激活（0：未激活，1：激活）',
    active_start_time     timestamp                              null comment '权限有效期开始时间',
    active_end_time       timestamp                              null comment '权限有效期结束时间',
    outer_net_access_flag tinyint(1)   default 1                 not null comment '外网访问标识（0：不可以访问，1：可以访问）',
    remark                varchar(255) default ''                not null comment '备注',
    admin_flag            tinyint(1)   default 0                 not null comment '是否超级管理员(0：不是，1：是)',
    del_flag              tinyint(1)   default 0                 not null comment '删除标志（0：未删除，1：已删除）',
    create_time           timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time           timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间',
    last_login_time       datetime     default CURRENT_TIMESTAMP not null comment '最后登录时间',
    last_modify_time      datetime     default CURRENT_TIMESTAMP not null comment '最后修改密码时间'
)
    comment '用户基础信息表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_user (create_time);

create index idx_employee_id
    on user_system.ac_user (employee_id);

create index idx_last_modify_time
    on user_system.ac_user (last_modify_time);

create index idx_phone
    on user_system.ac_user (phone);

create index idx_status_login_time
    on user_system.ac_user (active_status_flag, last_login_time);

create index idx_update_time
    on user_system.ac_user (update_time);

create table user_system.ac_user_city
(
    id          bigint auto_increment comment '标识列'
        primary key,
    user_id     bigint      default 0                 not null comment '用户id',
    city_code   varchar(10) default ''                not null comment '城市编码',
    city_name   varchar(50) default ''                not null comment '城市名称',
    create_time timestamp   default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time timestamp   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户城市关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_user_city (create_time);

create index idx_update_time
    on user_system.ac_user_city (update_time);

create table user_system.ac_user_company
(
    id          bigint unsigned auto_increment comment '自增ID，不用于业务'
        primary key,
    user_id     bigint    default 0                 not null comment '用户ID',
    company_id  bigint    default 0                 not null comment '租赁或加盟商编号',
    level       tinyint   default 1                 not null comment '租赁商或加盟商类型 1-一级 2-二级',
    create_time timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户租赁公司关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_user_company (create_time);

create index idx_org_id
    on user_system.ac_user_company (company_id);

create index idx_update_time
    on user_system.ac_user_company (update_time);

create table user_system.ac_user_logic_system
(
    id              bigint auto_increment comment '标识列'
        primary key,
    user_id         bigint    default 0                 not null comment '用户id',
    logic_system_id bigint    default 0                 not null comment '逻辑系统id',
    admin_flag      tinyint   default 0                 not null comment '是否系统管理员(0：不是，1：是)',
    create_time     timestamp default CURRENT_TIMESTAMP not null,
    update_time     timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户逻辑系统管理关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_user_logic_system (create_time);

create index idx_update_time
    on user_system.ac_user_logic_system (update_time);

create index idx_user_id
    on user_system.ac_user_logic_system (user_id);

create table user_system.ac_user_operate_group
(
    id               bigint unsigned auto_increment comment '标识列'
        primary key,
    user_id          bigint    default 0                 not null comment '用户id',
    operate_group_id bigint    default 0                 not null comment '操作组id',
    create_time      timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time      timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户操作组关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_user_operate_group (create_time);

create index idx_operate_group_id
    on user_system.ac_user_operate_group (operate_group_id);

create index idx_update_time
    on user_system.ac_user_operate_group (update_time);

create index idx_user_id
    on user_system.ac_user_operate_group (user_id);

create table user_system.ac_user_org
(
    id          bigint auto_increment
        primary key,
    user_id     bigint     default 0                 not null comment '用户ID',
    org_id      bigint     default 0                 not null comment '部门ID',
    admin_flag  tinyint(1) default 0                 not null comment '是否部门管理员(0：不是，1：是)',
    create_time timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    udpate_time timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    update_time timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '用户部门关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_user_org (create_time);

create index idx_update_time
    on user_system.ac_user_org (update_time);

create index idx_user_org
    on user_system.ac_user_org (user_id, org_id);

create table user_system.ac_user_position
(
    id          bigint unsigned auto_increment comment '标识列'
        primary key,
    user_id     bigint unsigned default '0'               not null comment '用户id',
    position_id bigint unsigned default '0'               not null comment '岗位id',
    create_time timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '授权系统-用户岗位关联表' engine = InnoDB
                                      charset = utf8mb4;

create index idx_create_time
    on user_system.ac_user_position (create_time);

create index idx_position_id
    on user_system.ac_user_position (position_id);

create index idx_update_time
    on user_system.ac_user_position (update_time);

create index idx_user_id
    on user_system.ac_user_position (user_id);

create table user_system.ac_user_role
(
    id          bigint auto_increment comment '标识列'
        primary key,
    user_id     bigint unsigned default '0'               not null comment '用户id',
    role_id     bigint unsigned default '0'               not null comment '角色id',
    create_time timestamp       default CURRENT_TIMESTAMP not null,
    update_time timestamp       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP
)
    comment '用户角色关联表' engine = InnoDB;

create index idx_create_time
    on user_system.ac_user_role (create_time);

create index idx_role_id
    on user_system.ac_user_role (role_id);

create index idx_update_time
    on user_system.ac_user_role (update_time);

create index idx_user_id
    on user_system.ac_user_role (user_id);

