package com.example.enums;

/**
 * 外网访问标识枚举
 */
public enum OuterNetAccessFlagEnum {
    
    CAN_ACCESS(0, "可以访问"),
    CANNOT_ACCESS(1, "不可以访问"),
    SERVER_ONLY(-1, "只允许server端访问"),
    DING_TALK_ONLY(2, "只允许钉钉端访问");
    
    private final Integer code;
    private final String desc;
    
    OuterNetAccessFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OuterNetAccessFlagEnum enumItem : OuterNetAccessFlagEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem.getDesc();
            }
        }
        return null;
    }
    
    public static OuterNetAccessFlagEnum getEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OuterNetAccessFlagEnum enumItem : OuterNetAccessFlagEnum.values()) {
            if (enumItem.getCode().equals(code)) {
                return enumItem;
            }
        }
        return null;
    }
} 