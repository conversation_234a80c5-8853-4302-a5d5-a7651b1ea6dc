@Configuration
public class MongoDataBusConfig {
    @Value("${mongodb.connectionsPerHost}")
    private Integer connectionsPerHost;
    @Value("${mongodb.threadsAllowedToBlockForConnectionMultiplier}")
    private Integer threadsAllowedToBlockForConnectionMultiplier;
    @Value("${mongodb.connectTimeout}")
    private Integer connectTimeout;
    @Value("${mongodb.maxWaitTime}")
    private Integer maxWaitTime;
    @Value("${mongodb.socketTimeout}")
    private Integer socketTimeout;

    @Value("${mongodb.dataBus.host-ports}")
    private String dataBusHostPorts;

    @Value("${mongodb.dataBus.password}")
    private String dataBusPassword;

    @Value("${mongodb.dataBus.username}")
    private String dataBusUsername;

    @Value("${mongodb.dataBus.database}")
    private String mongoDataBusDataBase;

    @Bean(name="mongoDataBusClient")
    public MongoClient mongoDataBusClient(){
        // 创建服务器地址列表
        String[] addresses = dataBusHostPorts.split(",");
        List<ServerAddress> serverAddressList = Lists.newArrayList();
        for(String addr : addresses){
            String[] addrArray = addr.split(":");
            ServerAddress serverAddress = new ServerAddress(addrArray[0], Integer.valueOf(addrArray[1]));
            serverAddressList.add(serverAddress);
        }
        
        // 创建MongoDB凭证
        MongoCredential credential = MongoCredential.createCredential(dataBusUsername, mongoDataBusDataBase, dataBusPassword.toCharArray());

        // 创建MongoDB客户端选项
        MongoClientOptions mongoClientOptions = MongoClientOptions.builder()
                .connectionsPerHost(connectionsPerHost)
                .threadsAllowedToBlockForConnectionMultiplier(threadsAllowedToBlockForConnectionMultiplier)
                .connectTimeout(connectTimeout)
                .maxWaitTime(maxWaitTime)
                .socketTimeout(socketTimeout)
                .serverSelectionTimeout(socketTimeout)
                .readPreference(ReadPreference.secondaryPreferred())
                .build();
        
        // MongoDB客户端会根据serverAddressList的大小自动决定使用单机模式还是集群模式
        return new MongoClient(serverAddressList, credential, mongoClientOptions);
    }

    @Bean(name="mongoDataBusTemplate")
    public MongoTemplate mongoDataBusTemplate(@Qualifier("mongoDataBusClient")MongoClient mongoDataBusClient){
        MongoTemplate mongoTemplate = new MongoTemplate(mongoDataBusClient, mongoDataBusDataBase);
        return mongoTemplate;
    }
}