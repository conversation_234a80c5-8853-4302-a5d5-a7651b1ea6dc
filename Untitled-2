	api RpcResult<List<UserDTO>> findUserByRoleId(Long roleId);
	
	/**
	 * 分页查询角色关联用户
	 * @param roleId 角色ID
	 * @param pageNum 页码
	 * @param pageSize 每页数量
	 * @return 用户列表及分页信息
	 */
	api RpcResult<PageDTO<UserDTO>> findUserByRoleIdWithPage(Long roleId, Integer pageNum, Integer pageSize);
	
	/**
	 * 根据角色ID和公司ID查询用户列表
	 * @param roleId 角色ID
	 * @param companyId 公司ID
	 * @return 用户列表
	 */
	api RpcResult<List<UserDTO>> findUserByRoleIdAndCompanyId(Long roleId, Long companyId);
	
	/**
	 * 分页查询角色关联用户（带公司ID筛选）
	 * @param roleId 角色ID
	 * @param companyId 公司ID
	 * @param pageNum 页码
	 * @param pageSize 每页数量
	 * @return 用户列表及分页信息
	 */
	api RpcResult<PageDTO<UserDTO>> findUserByRoleIdAndCompanyIdWithPage(Long roleId, Long companyId, Integer pageNum, Integer pageSize);

    @Override
    @LogAnnotation
    public RpcResult<List<UserDTO>> findUserByRoleId(Long roleId) {

        PreconditionUtil.checkNotNull(roleId, "角色编号为空");

        RpcResult<List<UserDTO>> rpcResult = new RpcResult();
        List<AcUserBO> acUserBOS;

        try {
            acUserBOS = acUserService.findUsersByRoleId(roleId);
            acUserService.setCitysByUserIdList(acUserBOS);
        } catch (BizException e) {
            log.warn("查询角色关联用户已知异常，roleId={}", roleId, e);
            return rpcResult.failure(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("查询角色关联用户未知异常，roleId={}", roleId, e);
            return rpcResult.failure(ErrorCodes.OTHER_EXCEPTION.getCode(), ErrorCodes.OTHER_EXCEPTION.getMessage());
        }

        // 组装返回结果
        List<UserDTO> userDTOS = acUserBOS.stream()
                .filter(acUserBO -> !Objects.isNull(acUserBO) &&
                        Objects.equals(acUserBO.getActiveStatusFlag(), CommonConstants.YES))
                .map(acUserBO -> {
                    UserDTO acUserDTO = new UserDTO();
                    acUserDTO.setId(acUserBO.getId());
                    acUserDTO.setUserName(acUserBO.getUserName());
                    acUserDTO.setEmployeeId(acUserBO.getEmployeeId());
                    acUserDTO.setTitle(acUserBO.getTitle());
                    acUserDTO.setPhone(acUserBO.getPhone());
                    acUserDTO.setEmail(acUserBO.getEmail());
                    acUserDTO.setType(acUserBO.getType());
                    if (acUserBO.getCitys() != null) {
                        acUserDTO.setCitys(acUserBO.getCitys());
                    }
                    return acUserDTO;
                }).collect(Collectors.toList());

        log.info("查询角色关联的用户列表，角色编号={}, 用户数量={}", roleId, userDTOS.size());

        return rpcResult.success(userDTOS);
    }

    @Override
    @LogAnnotation
    public RpcResult<PageDTO<UserDTO>> findUserByRoleIdWithPage(Long roleId, Integer pageNum, Integer pageSize) {
        PreconditionUtil.checkNotNull(roleId, "角色编号为空");
        PreconditionUtil.checkNotNull(pageNum, "页码不能为空");
        PreconditionUtil.checkNotNull(pageSize, "每页数量不能为空");

        RpcResult<PageDTO<UserDTO>> rpcResult = new RpcResult();
        try {
            // 查询总数
            int total = acUserService.countUsersByRoleId(roleId);
            List<AcUserBO> acUserBOS = Collections.emptyList();
            
            if (total > 0) {
                // 分页查询
                acUserBOS = acUserService.findUsersByRoleIdWithPage(roleId, pageNum, pageSize);
                // 设置城市信息
                acUserService.setCitysByUserIdList(acUserBOS);
            }
            
            // 组装返回结果
            List<UserDTO> userDTOS = acUserBOS.stream()
                    .filter(acUserBO -> !Objects.isNull(acUserBO) &&
                            Objects.equals(acUserBO.getActiveStatusFlag(), CommonConstants.YES))
                    .map(acUserBO -> {
                        UserDTO acUserDTO = new UserDTO();
                        acUserDTO.setId(acUserBO.getId());
                        acUserDTO.setUserName(acUserBO.getUserName());
                        acUserDTO.setEmployeeId(acUserBO.getEmployeeId());
                        acUserDTO.setTitle(acUserBO.getTitle());
                        acUserDTO.setPhone(acUserBO.getPhone());
                        acUserDTO.setEmail(acUserBO.getEmail());
                        acUserDTO.setType(acUserBO.getType());
                        if (acUserBO.getCitys() != null) {
                            acUserDTO.setCitys(acUserBO.getCitys());
                        }
                        return acUserDTO;
                    }).collect(Collectors.toList());

            // 创建分页结果
            PageDTO<UserDTO> pageDTO = new PageDTO<>();
            pageDTO.setList(userDTOS);
            pageDTO.setTotal(total);
            pageDTO.setPageNum(pageNum);
            pageDTO.setPageSize(pageSize);
            
            log.info("分页查询角色关联的用户列表，角色编号={}, 页码={}, 每页数量={}, 总数={}, 返回数量={}", 
                    roleId, pageNum, pageSize, total, userDTOS.size());
            
            return rpcResult.success(pageDTO);
        } catch (BizException e) {
            log.warn("分页查询角色关联用户已知异常，roleId={}, pageNum={}, pageSize={}", roleId, pageNum, pageSize, e);
            return rpcResult.failure(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error("分页查询角色关联用户未知异常，roleId={}, pageNum={}, pageSize={}", roleId, pageNum, pageSize, e);
            return rpcResult.failure(ErrorCodes.OTHER_EXCEPTION.getCode(), ErrorCodes.OTHER_EXCEPTION.getMessage());
        }
    }

    List<AcUserBO> findUsersByRoleId(Long roleId);
    
    /**
     * 统计角色关联的用户数量
     * @param roleId 角色ID
     * @return 用户数量
     */
    int countUsersByRoleId(Long roleId);
    
    /**
     * 分页查询角色关联的用户
     * @param roleId 角色ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 用户列表
     */
    List<AcUserBO> findUsersByRoleIdWithPage(Long roleId, int pageNum, int pageSize);
    
    /**
     * 根据角色ID和公司ID查询用户列表
     * @param roleId 角色ID
     * @param companyId 公司ID
     * @return 用户列表
     */
    List<AcUserBO> findUsersByRoleIdAndCompanyId(Long roleId, Long companyId);
    
    /**
     * 统计指定角色和公司关联的用户数量
     * @param roleId 角色ID
     * @param companyId 公司ID
     * @return 用户数量
     */
    int countUsersByRoleIdAndCompanyId(Long roleId, Long companyId);
    
    /**
     * 分页查询指定角色和公司关联的用户
     * @param roleId 角色ID
     * @param companyId 公司ID
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 用户列表
     */
    List<AcUserBO> findUsersByRoleIdAndCompanyIdWithPage(Long roleId, Long companyId, int pageNum, int pageSize);

    api RpcResult<PageListResult<UserDTO>> findUserByRoleIdWithPage(PageQueryCondition<UserRoleQueryDTO> queryCondition);
}

public List<AcUserBO> findUsersByRoleId(Long roleId) {

    PreconditionUtil.checkArgument(null != roleId, ErrorCodes.DEFAULT_CHECK_COMMON_ERROR,
        "角色编号不能为空");

    List<AcUserDO> result = acUserDAO.findUsersByRoleId(roleId);

    List<AcUserBO> resultBOs = AcUserTransfer.toBOList(result);

    return resultBOs;
}

@Override
public int countUsersByRoleId(Long roleId) {
    PreconditionUtil.checkArgument(null != roleId, ErrorCodes.DEFAULT_CHECK_COMMON_ERROR,
        "角色编号不能为空");
        
    return acUserDAO.countUsersByRoleId(roleId);
}

@Override
public List<AcUserBO> findUsersByRoleIdWithPage(Long roleId, int pageNum, int pageSize) {
    PreconditionUtil.checkArgument(null != roleId, ErrorCodes.DEFAULT_CHECK_COMMON_ERROR,
        "角色编号不能为空");
    
    // 计算偏移量
    int offset = (pageNum - 1) * pageSize;
    
    List<AcUserDO> result = acUserDAO.findUsersByRoleIdWithPage(roleId, offset, pageSize);
    
    List<AcUserBO> resultBOs = AcUserTransfer.toBOList(result);
    
    return resultBOs;
}

<select id="findUsersByRoleId" resultMap="BaseResultMap" parameterType="java.lang.Long">
    SELECT
    <include refid="Base_Column_List_DO"/>
    FROM ac_user u
    INNER JOIN ac_user_role ur ON u.id = ur.user_id
    WHERE ur.role_id = #{roleId,jdbcType=BIGINT}
</select>

<select id="countUsersByRoleId" resultType="java.lang.Integer" parameterType="java.lang.Long">
    SELECT COUNT(1)
    FROM ac_user u
    INNER JOIN ac_user_role ur ON u.id = ur.user_id
    WHERE ur.role_id = #{roleId,jdbcType=BIGINT}
</select>

<select id="findUsersByRoleIdWithPage" resultMap="BaseResultMap" parameterType="java.lang.Long">
    SELECT
    <include refid="Base_Column_List_DO"/>
    FROM ac_user u
    INNER JOIN ac_user_role ur ON u.id = ur.user_id
    WHERE ur.role_id = #{roleId,jdbcType=BIGINT}
</select>

<select id="findUsersByRoleIdAndCompanyIdWithPage" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List_DO"/>
    FROM ac_user u
    INNER JOIN ac_user_role ur ON u.id = ur.user_id
    INNER JOIN ac_user_company uc ON u.id = uc.user_id
    WHERE ur.role_id = #{roleId,jdbcType=BIGINT}
    <if test="companyId != null">
        AND uc.company_id = #{companyId,jdbcType=BIGINT}
    </if>
</select>

<insert id="insert" parameterType="com.caocao.bss.authserver.dal.dataobject.ac.AcUserCompanyDO" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO ac_user_company (user_id, company_id)
    VALUES (#{userId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT})
</insert>

public RpcResult<PageListResult<UserDTO>> findUserByRoleIdWithPage(PageQueryCondition<UserRoleQueryDTO> queryCondition) {
    UserRoleQueryDTO queryDTO = queryCondition.getData();
    Long roleId = queryDTO.getRoleId();
    Long companyId = queryDTO.getCompanyId();
    PreconditionUtil.checkNotNull(roleId, "角色编号为空");

    RpcResult<PageListResult<UserDTO>> rpcResult = new RpcResult();
    try {
        com.caocao.bss.authserver.common.model.PageQueryCondition pageQueryCondition = BeanUtil.copyProperties(queryCondition, com.caocao.bss.authserver.common.model.PageQueryCondition.class);
        Page page = PageHelperAdaptor.preparePage(pageQueryCondition);

        // 查询数据
        List<AcUserBO> acUserBOS = acUserService.findUsersByRoleIdAndCompanyIdWithPage(roleId, companyId);
        // 设置城市信息
        acUserService.setCitysByUserIdList(acUserBOS);

        // 组装返回结果
        List<UserDTO> userDTOS = acUserBOS.stream()
                .filter(acUserBO -> !Objects.isNull(acUserBO) &&
                        Objects.equals(acUserBO.getActiveStatusFlag(), CommonConstants.YES))
                .map(acUserBO -> {
                    UserDTO acUserDTO = new UserDTO();
                    acUserDTO.setId(acUserBO.getId());
                    acUserDTO.setUserName(acUserBO.getUserName());
                    acUserDTO.setEmployeeId(acUserBO.getEmployeeId());
                    acUserDTO.setTitle(acUserBO.getTitle());
                    acUserDTO.setPhone(acUserBO.getPhone());
                    acUserDTO.setEmail(acUserBO.getEmail());
                    acUserDTO.setType(acUserBO.getType());
                    if (acUserBO.getCitys() != null) {
                        acUserDTO.setCitys(acUserBO.getCitys());
                    }
                    return acUserDTO;
                }).collect(Collectors.toList());

        // 创建分页结果
        PageListResult<UserDTO> pageListResult = new PageListResult<>();
        pageListResult.setList(userDTOS);
        pageListResult.setTotal(acUserBOS.size());
        pageListResult.setPageNum(page.getPageNum());
        pageListResult.setPageSize(page.getPageSize());
        
        log.info("分页查询角色关联的用户列表，角色编号={}, 页码={}, 每页数量={}, 总数={}, 返回数量={}", 
                roleId, page.getPageNum(), page.getPageSize(), acUserBOS.size(), userDTOS.size());
        
        return rpcResult.success(pageListResult);
    } catch (BizException e) {
        log.warn("分页查询角色关联用户已知异常，roleId={}, companyId={}", roleId, companyId, e);
        return rpcResult.failure(e.getErrorCode(), e.getMessage());
    } catch (Exception e) {
        log.error("分页查询角色关联用户未知异常，roleId={}, companyId={}", roleId, companyId, e);
        return rpcResult.failure(ErrorCodes.OTHER_EXCEPTION.getCode(), ErrorCodes.OTHER_EXCEPTION.getMessage());
    }
}

public class UserRoleQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Long roleId;
    private Long companyId;
    
    public Long getRoleId() {
        return roleId;
    }
    
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
    
    public Long getCompanyId() {
        return companyId;
    }
    
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}

List<AcUserDO> findUsersByRoleIdWithPage(Long roleId);

List<AcUserDO> findUsersByRoleIdAndCompanyIdWithPage(Long roleId, Long companyId);

public List<AcUserBO> findUsersByRoleIdWithPage(Long roleId) {
    PreconditionUtil.checkArgument(null != roleId, ErrorCodes.DEFAULT_CHECK_COMMON_ERROR,
            "角色编号不能为空");

    List<AcUserDO> result = acUserDAO.findUsersByRoleIdWithPage(roleId);

    return AcUserTransfer.toBOList(result);
}

public List<AcUserBO> findUsersByRoleIdAndCompanyIdWithPage(Long roleId, Long companyId) {
    PreconditionUtil.checkArgument(null != roleId, ErrorCodes.DEFAULT_CHECK_COMMON_ERROR,
            "角色编号不能为空");

    List<AcUserDO> result = acUserDAO.findUsersByRoleIdAndCompanyIdWithPage(roleId, companyId);

    return AcUserTransfer.toBOList(result);
}