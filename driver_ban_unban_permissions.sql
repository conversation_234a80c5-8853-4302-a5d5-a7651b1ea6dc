-- ========================================
-- 查询1：拥有司机封号权限的用户信息
-- ========================================
-- 封号权限包括：/driverInfo/disableAccount, /driverInfo/forceBanAccount, /driverInfo/batchDisableAccountNew

SELECT DISTINCT
    usr.user_name AS '姓名',
    usr.employee_id AS '工号',
    GROUP_CONCAT(DISTINCT org.org_name) AS '部门',
    usr.title AS '岗位',
    '是' AS '是否拥有司机封号权限'
FROM ac_user usr
LEFT JOIN ac_user_org usr_org ON usr_org.user_id = usr.id
LEFT JOIN ac_org org ON usr_org.org_id = org.id
WHERE usr.active_status_flag = 1
AND EXISTS (
    -- 通过角色获得封号权限
    SELECT 1 FROM ac_user_role usr_role
    INNER JOIN ac_role role ON usr_role.role_id = role.id AND role.del_flag = 0
    INNER JOIN ac_role_operate_group role_og ON role_og.role_id = role.id
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = role_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    WHERE usr_role.user_id = usr.id 
    AND url_res.url IN (
        '/driverInfo/disableAccount',
        '/driverInfo/forceBanAccount', 
        '/driverInfo/batchDisableAccountNew'
    )
    
    UNION
    
    -- 通过岗位获得封号权限
    SELECT 1 FROM ac_user_position usr_position
    INNER JOIN ac_position position ON usr_position.position_id = position.id AND position.del_flag = 0
    INNER JOIN ac_position_operate_group position_og ON position_og.position_id = position.id
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = position_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    WHERE usr_position.user_id = usr.id
    AND url_res.url IN (
        '/driverInfo/disableAccount',
        '/driverInfo/forceBanAccount', 
        '/driverInfo/batchDisableAccountNew'
    )
    
    UNION
    
    -- 直接关联封号权限
    SELECT 1 FROM ac_user_operate_group user_og
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = user_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    WHERE user_og.user_id = usr.id
    AND url_res.url IN (
        '/driverInfo/disableAccount',
        '/driverInfo/forceBanAccount', 
        '/driverInfo/batchDisableAccountNew'
    )
)
GROUP BY usr.user_name, usr.employee_id, usr.title
ORDER BY usr.user_name ASC, usr.employee_id ASC;


-- ========================================
-- 查询2：拥有司机解封权限的用户信息
-- ========================================
-- 解封权限包括：/driverOperate/enableAccount, /driverInfo/enableRuleBanAccount

SELECT DISTINCT
    usr.user_name AS '姓名',
    usr.employee_id AS '工号',
    GROUP_CONCAT(DISTINCT org.org_name) AS '部门',
    usr.title AS '岗位',
    '是' AS '是否拥有司机解封权限'
FROM ac_user usr
LEFT JOIN ac_user_org usr_org ON usr_org.user_id = usr.id
LEFT JOIN ac_org org ON usr_org.org_id = org.id
WHERE usr.active_status_flag = 1
AND EXISTS (
    -- 通过角色获得解封权限
    SELECT 1 FROM ac_user_role usr_role
    INNER JOIN ac_role role ON usr_role.role_id = role.id AND role.del_flag = 0
    INNER JOIN ac_role_operate_group role_og ON role_og.role_id = role.id
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = role_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    WHERE usr_role.user_id = usr.id 
    AND url_res.url IN (
        '/driverOperate/enableAccount',
        '/driverInfo/enableRuleBanAccount'
    )
    
    UNION
    
    -- 通过岗位获得解封权限
    SELECT 1 FROM ac_user_position usr_position
    INNER JOIN ac_position position ON usr_position.position_id = position.id AND position.del_flag = 0
    INNER JOIN ac_position_operate_group position_og ON position_og.position_id = position.id
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = position_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    WHERE usr_position.user_id = usr.id
    AND url_res.url IN (
        '/driverOperate/enableAccount',
        '/driverInfo/enableRuleBanAccount'
    )
    
    UNION
    
    -- 直接关联解封权限
    SELECT 1 FROM ac_user_operate_group user_og
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = user_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    WHERE user_og.user_id = usr.id
    AND url_res.url IN (
        '/driverOperate/enableAccount',
        '/driverInfo/enableRuleBanAccount'
    )
)
GROUP BY usr.user_name, usr.employee_id, usr.title
ORDER BY usr.user_name ASC, usr.employee_id ASC;


-- ========================================
-- 详细版本：显示具体拥有哪些权限URL
-- ========================================
SELECT 
    merge_tab.姓名,
    merge_tab.工号,
    merge_tab.用户类型,
    GROUP_CONCAT(DISTINCT merge_tab.部门) AS 部门,
    merge_tab.岗位,
    GROUP_CONCAT(DISTINCT merge_tab.权限URL) AS 拥有的权限URL,
    CASE 
        WHEN GROUP_CONCAT(DISTINCT merge_tab.权限URL) LIKE '%disableAccount%' 
          OR GROUP_CONCAT(DISTINCT merge_tab.权限URL) LIKE '%forceBanAccount%'
          OR GROUP_CONCAT(DISTINCT merge_tab.权限URL) LIKE '%batchDisableAccountNew%' 
        THEN '是'
        ELSE '否'
    END AS 是否拥有司机封号权限,
    CASE 
        WHEN GROUP_CONCAT(DISTINCT merge_tab.权限URL) LIKE '%enableAccount%' 
          OR GROUP_CONCAT(DISTINCT merge_tab.权限URL) LIKE '%enableRuleBanAccount%'
        THEN '是'
        ELSE '否'
    END AS 是否拥有司机解封权限,
    GROUP_CONCAT(DISTINCT merge_tab.权限来源) AS 权限来源
FROM (
    -- 通过角色获得的权限
    SELECT
        usr.user_name AS '姓名',
        usr.employee_id AS '工号',
        CASE 
            WHEN usr.type = 0 THEN '曹操'
            WHEN usr.type = 1 THEN '观海'
            ELSE '未知'
        END AS '用户类型',
        GROUP_CONCAT(DISTINCT org.org_name) AS '部门',
        usr.title AS '岗位',
        url_res.url AS '权限URL',
        '角色' AS '权限来源'
    FROM ac_user usr
    INNER JOIN ac_user_role usr_role ON usr_role.user_id = usr.id
    INNER JOIN ac_role role ON usr_role.role_id = role.id AND role.del_flag = 0
    INNER JOIN ac_role_operate_group role_og ON role_og.role_id = role.id
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = role_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    LEFT JOIN ac_user_org usr_org ON usr_org.user_id = usr.id
    LEFT JOIN ac_org org ON usr_org.org_id = org.id
    WHERE usr.active_status_flag = 1
    AND url_res.url IN (
        '/driverInfo/disableAccount',
        '/driverInfo/forceBanAccount',
        '/driverOperate/enableAccount',
        '/driverInfo/enableRuleBanAccount',
        '/driverInfo/batchDisableAccountNew'
    )
    GROUP BY usr.user_name, usr.employee_id, usr.type, usr.title, url_res.url

    UNION ALL

    -- 通过岗位获得的权限
    SELECT
        usr.user_name AS '姓名',
        usr.employee_id AS '工号',
        CASE 
            WHEN usr.type = 0 THEN '曹操'
            WHEN usr.type = 1 THEN '观海'
            ELSE '未知'
        END AS '用户类型',
        GROUP_CONCAT(DISTINCT org.org_name) AS '部门',
        usr.title AS '岗位',
        url_res.url AS '权限URL',
        '岗位' AS '权限来源'
    FROM ac_user usr
    INNER JOIN ac_user_position usr_position ON usr_position.user_id = usr.id
    INNER JOIN ac_position position ON usr_position.position_id = position.id AND position.del_flag = 0
    INNER JOIN ac_position_operate_group position_og ON position_og.position_id = position.id
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = position_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    LEFT JOIN ac_user_org usr_org ON usr_org.user_id = usr.id
    LEFT JOIN ac_org org ON usr_org.org_id = org.id
    WHERE usr.active_status_flag = 1
    AND url_res.url IN (
        '/driverInfo/disableAccount',
        '/driverInfo/forceBanAccount',
        '/driverOperate/enableAccount',
        '/driverInfo/enableRuleBanAccount',
        '/driverInfo/batchDisableAccountNew'
    )
    GROUP BY usr.user_name, usr.employee_id, usr.type, usr.title, url_res.url

    UNION ALL

    -- 直接关联的权限
    SELECT
        usr.user_name AS '姓名',
        usr.employee_id AS '工号',
        CASE 
            WHEN usr.type = 0 THEN '曹操'
            WHEN usr.type = 1 THEN '观海'
            ELSE '未知'
        END AS '用户类型',
        GROUP_CONCAT(DISTINCT org.org_name) AS '部门',
        usr.title AS '岗位',
        url_res.url AS '权限URL',
        '直接关联' AS '权限来源'
    FROM ac_user usr
    INNER JOIN ac_user_operate_group user_og ON user_og.user_id = usr.id
    INNER JOIN ac_operate_group_url_resource og_url ON og_url.operate_group_id = user_og.operate_group_id
    INNER JOIN ac_url_resource url_res ON og_url.url_resource_id = url_res.id
    LEFT JOIN ac_user_org usr_org ON usr_org.user_id = usr.id
    LEFT JOIN ac_org org ON usr_org.org_id = org.id
    WHERE usr.active_status_flag = 1
    AND url_res.url IN (
        '/driverInfo/disableAccount',
        '/driverInfo/forceBanAccount',
        '/driverOperate/enableAccount',
        '/driverInfo/enableRuleBanAccount',
        '/driverInfo/batchDisableAccountNew'
    )
    GROUP BY usr.user_name, usr.employee_id, usr.type, usr.title, url_res.url
) AS merge_tab
GROUP BY merge_tab.姓名, merge_tab.工号, merge_tab.用户类型, merge_tab.岗位
ORDER BY merge_tab.姓名 ASC, merge_tab.工号 ASC; 